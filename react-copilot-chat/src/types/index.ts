/**
 * Global TypeScript type definitions
 */

import React, { ReactNode } from 'react'

// Environment variables
export interface ImportMetaEnv {
  readonly VITE_DIRECT_LINE_TOKEN: string
  readonly VITE_BOT_NAME: string
  readonly VITE_WELCOME_MESSAGE: string
  readonly VITE_ENVIRONMENT: string
}

declare global {
  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}

// Bot Framework WebChat types
export interface DirectLineOptions {
  token?: string
  secret?: string
  domain?: string
  pollingInterval?: number
}

export interface WebChatStyleSet {
  rootHeight?: string | number
  rootWidth?: string | number
  primaryColor?: string
  accent?: string
  fontFamily?: string
  bubbleBackground?: string
  bubbleBorderColor?: string
  bubbleBorderRadius?: number
  bubbleFromUserBackground?: string
  bubbleFromUserTextColor?: string
  sendBoxBackground?: string
  sendBoxBorderColor?: string
  sendBoxButtonColor?: string
  sendBoxButtonColorOnHover?: string
  sendBoxTextColor?: string
  suggestedActionBackground?: string
  suggestedActionBorderColor?: string
  suggestedActionTextColor?: string
  suggestedActionBackgroundOnHover?: string
  suggestedActionTextColorOnHover?: string
  timestampColor?: string
  typingAnimationBackgroundImage?: string
  [key: string]: string | number | undefined
}

export interface ConnectionStatus {
  UNINITIALIZED: 0
  CONNECTING: 1
  ONLINE: 2
  EXPIRED_TOKEN: 3
  FAILED_TO_CONNECT: 4
  ENDED: 5
}

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'success'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  style?: React.CSSProperties
}

export interface CardProps extends BaseComponentProps {
  variant?: 'default' | 'elevated' | 'outlined'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  style?: React.CSSProperties
}

export interface StatusIndicatorProps extends BaseComponentProps {
  status: 'success' | 'error' | 'warning' | 'info' | 'loading'
  title: string
  description?: string
  icon?: string
}

// WebChat component types
export interface WebChatComponentState {
  directLine: unknown | null
  error: string
  isInitializing: boolean
}

export interface TokenInfo {
  isValid: boolean
  source: 'environment' | 'manual'
  masked?: string
}

// Utility types
export type Variant<T extends Record<string, unknown>> = keyof T
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
export type Color = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'

// Theme types (re-exported from theme)
export type { Theme, MediaQueries } from '@/theme'

// Error types
export interface WebChatError {
  message: string
  code?: string
  details?: unknown
}

export interface ConnectionError extends WebChatError {
  type: 'CONNECTION_ERROR'
  retryable: boolean
}

export interface TokenError extends WebChatError {
  type: 'TOKEN_ERROR'
  tokenSource: 'environment' | 'manual'
}

// Event types
export interface WebChatEvent {
  type: string
  payload?: unknown
  timestamp: Date
}

export interface ConnectionEvent extends WebChatEvent {
  type: 'CONNECTION_STATUS_CHANGED'
  payload: {
    status: keyof ConnectionStatus
    previousStatus?: keyof ConnectionStatus
  }
}

export interface MessageEvent extends WebChatEvent {
  type: 'MESSAGE_SENT' | 'MESSAGE_RECEIVED'
  payload: {
    message: unknown
    from: 'user' | 'bot'
  }
}
