/**
 * Bot Service Diagnostics
 * Comprehensive testing tools for DirectLine and bot connectivity
 */

export interface DiagnosticResult {
  step: string
  success: boolean
  message: string
  data?: unknown
  error?: string
}

export interface BotDiagnostics {
  token: string
  results: DiagnosticResult[]
  overallSuccess: boolean
  summary: string
}

export async function runBotDiagnostics(token: string): Promise<BotDiagnostics> {
  const results: DiagnosticResult[] = []
  let overallSuccess = true

  console.log('🔍 Starting comprehensive bot diagnostics...')

  // Step 1: Validate token format
  console.log('📋 Step 1: Validating token format...')
  try {
    if (!token || token.trim().length === 0) {
      throw new Error('Token is empty or undefined')
    }
    
    if (token.length < 50) {
      throw new Error('Token appears to be too short')
    }
    
    if (!token.includes('.')) {
      throw new Error('Token does not appear to be in the correct format (missing dots)')
    }
    
    results.push({
      step: 'Token Format Validation',
      success: true,
      message: `Token format appears valid (${token.length} characters)`
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    results.push({
      step: 'Token Format Validation',
      success: false,
      message: 'Token format validation failed',
      error: errorMessage
    })
    overallSuccess = false
  }

  // Step 2: Test DirectLine API connectivity
  console.log('🌐 Step 2: Testing DirectLine API connectivity...')
  let conversationId = ''
  try {
    const response = await fetch('https://directline.botframework.com/v3/directline/conversations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    conversationId = data.conversationId
    
    results.push({
      step: 'DirectLine API Connectivity',
      success: true,
      message: `Successfully created conversation: ${conversationId}`,
      data: data
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    results.push({
      step: 'DirectLine API Connectivity',
      success: false,
      message: 'Failed to connect to DirectLine API',
      error: errorMessage
    })
    overallSuccess = false
  }

  // Step 3: Test sending a message to the bot
  if (conversationId) {
    console.log('📤 Step 3: Testing message sending to bot...')
    try {
      const messageResponse = await fetch(`https://directline.botframework.com/v3/directline/conversations/${conversationId}/activities`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'message',
          text: 'Hello from diagnostics',
          from: {
            id: 'diagnostic-user',
            name: 'Diagnostic User'
          }
        })
      })

      if (!messageResponse.ok) {
        const errorText = await messageResponse.text()
        throw new Error(`HTTP ${messageResponse.status}: ${messageResponse.statusText} - ${errorText}`)
      }

      const messageData = await messageResponse.json()
      
      results.push({
        step: 'Message Sending',
        success: true,
        message: `Successfully sent message to bot`,
        data: messageData
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      results.push({
        step: 'Message Sending',
        success: false,
        message: 'Failed to send message to bot',
        error: errorMessage
      })
      overallSuccess = false
    }

    // Step 4: Test receiving messages from the bot
    console.log('📥 Step 4: Testing message receiving from bot...')
    try {
      // Wait a moment for the bot to respond
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const activitiesResponse = await fetch(`https://directline.botframework.com/v3/directline/conversations/${conversationId}/activities`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!activitiesResponse.ok) {
        const errorText = await activitiesResponse.text()
        throw new Error(`HTTP ${activitiesResponse.status}: ${activitiesResponse.statusText} - ${errorText}`)
      }

      const activitiesData = await activitiesResponse.json()
      const botActivities = activitiesData.activities?.filter((activity: any) => 
        activity.from?.id !== 'diagnostic-user' && activity.type === 'message'
      ) || []
      
      if (botActivities.length > 0) {
        results.push({
          step: 'Bot Response',
          success: true,
          message: `Bot responded with ${botActivities.length} message(s)`,
          data: botActivities
        })
      } else {
        results.push({
          step: 'Bot Response',
          success: false,
          message: 'Bot did not respond to the test message. This could indicate the bot service is not running or not properly configured.',
          data: activitiesData
        })
        overallSuccess = false
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      results.push({
        step: 'Bot Response',
        success: false,
        message: 'Failed to retrieve bot response',
        error: errorMessage
      })
      overallSuccess = false
    }
  }

  // Step 5: Test WebSocket connectivity (if supported)
  console.log('🔌 Step 5: Testing WebSocket connectivity...')
  try {
    // This is a simplified test - in reality, WebChat handles WebSocket connections
    const wsTestResult = await testWebSocketConnectivity(token)
    results.push({
      step: 'WebSocket Connectivity',
      success: wsTestResult.success,
      message: wsTestResult.message,
      data: wsTestResult.data
    })
    
    if (!wsTestResult.success) {
      overallSuccess = false
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    results.push({
      step: 'WebSocket Connectivity',
      success: false,
      message: 'WebSocket connectivity test failed',
      error: errorMessage
    })
  }

  // Generate summary
  const successfulSteps = results.filter(r => r.success).length
  const totalSteps = results.length
  const summary = overallSuccess 
    ? `All diagnostics passed (${successfulSteps}/${totalSteps}). Bot should be working correctly.`
    : `${successfulSteps}/${totalSteps} diagnostics passed. Issues detected that may prevent bot functionality.`

  console.log('📊 Diagnostics complete:', summary)

  return {
    token: token.substring(0, 20) + '...' + token.substring(token.length - 10), // Masked token
    results,
    overallSuccess,
    summary
  }
}

async function testWebSocketConnectivity(token: string): Promise<{ success: boolean; message: string; data?: unknown }> {
  // For now, we'll just check if WebSocket is available in the browser
  // A full WebSocket test would require establishing a DirectLine stream connection
  
  if (typeof WebSocket === 'undefined') {
    return {
      success: false,
      message: 'WebSocket is not available in this environment'
    }
  }

  // Test basic WebSocket capability
  try {
    // We can't easily test the actual DirectLine WebSocket without more complex setup
    // So we'll just verify WebSocket support exists
    return {
      success: true,
      message: 'WebSocket support is available',
      data: { webSocketSupported: true }
    }
  } catch (error) {
    return {
      success: false,
      message: 'WebSocket test failed',
      data: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

export function printDiagnosticResults(diagnostics: BotDiagnostics): void {
  console.log('\n🔍 BOT DIAGNOSTICS REPORT')
  console.log('========================')
  console.log(`Token: ${diagnostics.token}`)
  console.log(`Overall Status: ${diagnostics.overallSuccess ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Summary: ${diagnostics.summary}`)
  console.log('\nDetailed Results:')
  
  diagnostics.results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌'
    console.log(`${index + 1}. ${status} ${result.step}: ${result.message}`)
    
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
    
    if (result.data && !result.success) {
      console.log(`   Data:`, result.data)
    }
  })
  
  if (!diagnostics.overallSuccess) {
    console.log('\n🔧 TROUBLESHOOTING RECOMMENDATIONS:')
    
    const failedSteps = diagnostics.results.filter(r => !r.success)
    
    if (failedSteps.some(s => s.step === 'Token Format Validation')) {
      console.log('• Check your Direct Line token format and ensure it\'s correctly set in the .env file')
    }
    
    if (failedSteps.some(s => s.step === 'DirectLine API Connectivity')) {
      console.log('• Verify your Direct Line token is valid and not expired')
      console.log('• Check your network connection and firewall settings')
    }
    
    if (failedSteps.some(s => s.step === 'Bot Response')) {
      console.log('• Ensure your bot service is running and properly configured')
      console.log('• Check the bot\'s endpoint URL in Azure Bot Service settings')
      console.log('• Verify the bot is responding to messages (test in Azure Portal)')
    }
    
    if (failedSteps.some(s => s.step === 'WebSocket Connectivity')) {
      console.log('• Check if WebSocket connections are blocked by your network/firewall')
      console.log('• Try using a different network or disable VPN if applicable')
    }
  }
  
  console.log('\n========================\n')
}
