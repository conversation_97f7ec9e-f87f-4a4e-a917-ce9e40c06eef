.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  text-align: center;
  color: white;
  padding: 2rem 1rem 1rem 1rem;
}

.app-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.app-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.app-footer {
  text-align: center;
  color: white;
  padding: 1rem;
  opacity: 0.8;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .app-main {
    padding: 0.5rem;
  }
}

/* Rich Card Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Rich Card Responsive Styles */
@media (max-width: 768px) {
  .rich-card {
    margin: 8px 0;
    border-radius: 6px;
  }

  .rich-card-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .rich-card-buttons button {
    width: 100%;
    margin-bottom: 8px;
  }

  .rich-card-image {
    max-height: 200px;
  }

  .rich-card-carousel {
    height: auto;
  }

  .rich-card-carousel .navigation-button {
    display: none;
  }
}

@media (max-width: 480px) {
  .rich-card {
    margin: 4px 0;
    border-radius: 4px;
  }

  .rich-card-title {
    font-size: 16px;
  }

  .rich-card-text {
    font-size: 14px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .rich-card {
    border: 2px solid currentColor;
  }

  .rich-card-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .rich-card,
  .rich-card-button,
  .rich-card-carousel {
    transition: none;
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .rich-card {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .rich-card-header,
  .rich-card-footer {
    background-color: #111827;
  }
}
