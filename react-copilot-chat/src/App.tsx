import React, { useState } from 'react'
import WebChatComponent from './components/WebChatComponent'
import { DemoPage } from './components/DemoPage'
import { TokenTester } from './components/TokenTester'
import { TroubleshootingGuide } from './components/TroubleshootingGuide'
import { SampleRichContentDemo } from './components/richCards/SampleRichContent'
import { ModeDropdown, ViewMode } from './components/ui/ModeDropdown'
import { theme } from './theme'
import './App.css'

/**
 * Main App component
 * Provides the layout structure for the Copilot Studio WebChat application
 * Now includes both full-page and floating chat modes
 */
const App: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('demo')

  const handleModeChange = (mode: ViewMode) => {
    setViewMode(mode)
  }



  if (viewMode === 'demo') {
    return (
      <>
        <ModeDropdown currentMode={viewMode} onModeChange={handleModeChange} />
        <DemoPage />
      </>
    )
  }

  if (viewMode === 'debug') {
    const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
    return (
      <>
        <ModeDropdown currentMode={viewMode} onModeChange={handleModeChange} />
        <div style={{ padding: '20px', fontFamily: theme.fonts.families.primary }}>
          <h1 style={{ color: theme.colors.brandRed, marginBottom: '20px' }}>
            🧪 Direct Line Token Debug Mode
          </h1>
          <p style={{ marginBottom: '20px', color: theme.colors.neutral700 }}>
            This debug mode helps diagnose connection issues with your Direct Line token.
          </p>
          {envToken ? (
            <>
              <TokenTester token={envToken} />
              <TroubleshootingGuide />
            </>
          ) : (
            <>
              <div style={{
                padding: '20px',
                backgroundColor: theme.colors.brandLightRed,
                borderRadius: '8px',
                border: `1px solid ${theme.colors.brandRed}`,
                color: theme.colors.brandDarkRed,
                marginBottom: '20px'
              }}>
                <strong>❌ No Direct Line token found</strong>
                <p>Please set VITE_DIRECT_LINE_TOKEN in your .env file.</p>
              </div>
              <TroubleshootingGuide />
            </>
          )}
        </div>
      </>
    )
  }

  if (viewMode === 'richcards') {
    return (
      <>
        <ModeDropdown currentMode={viewMode} onModeChange={handleModeChange} />
        <SampleRichContentDemo />
      </>
    )
  }

  return (
    <>
      <ModeDropdown currentMode={viewMode} onModeChange={handleModeChange} />
      <div className="app">
        <header className="app-header">
          <h1>🤖 Copilot Studio Web Chat</h1>
          <p>Microsoft Bot Framework Integration POC - TypeScript Version</p>
        </header>

        <main className="app-main">
          <WebChatComponent />
        </main>

        <footer className="app-footer">
          <p>Built with React + TypeScript + Vite + Bot Framework Web Chat</p>
        </footer>
      </div>
    </>
  )
}

export default App
