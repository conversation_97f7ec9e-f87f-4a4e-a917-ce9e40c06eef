import React, { useState } from 'react'
import WebChatComponent from './components/WebChatComponent'
import { DemoPage } from './components/DemoPage'
import { theme } from './theme'
import './App.css'

/**
 * Main App component
 * Provides the layout structure for the Copilot Studio WebChat application
 * Now includes both full-page and floating chat modes
 */
const App: React.FC = () => {
  const [viewMode, setViewMode] = useState<'demo' | 'fullpage'>('demo')

  const toggleViewMode = () => {
    setViewMode(prev => prev === 'demo' ? 'fullpage' : 'demo')
  }

  const switcherStyles: React.CSSProperties = {
    position: 'fixed',
    top: '20px',
    left: '20px',
    zIndex: 1001,
    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
    color: theme.colors.neutralWhite,
    border: 'none',
    padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
    borderRadius: '8px',
    fontSize: theme.fonts.sizes.sm,
    fontWeight: theme.fonts.weights.medium,
    cursor: 'pointer',
    boxShadow: theme.shadows.medium,
    transition: 'all 0.2s ease-in-out',
  }

  if (viewMode === 'demo') {
    return (
      <>
        <button
          style={switcherStyles}
          onClick={toggleViewMode}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = theme.shadows.large
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = theme.shadows.medium
          }}
          title="Switch to full-page mode"
        >
          📄 Full Page Mode
        </button>
        <DemoPage />
      </>
    )
  }

  return (
    <>
      <button
        style={switcherStyles}
        onClick={toggleViewMode}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)'
          e.currentTarget.style.boxShadow = theme.shadows.large
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)'
          e.currentTarget.style.boxShadow = theme.shadows.medium
        }}
        title="Switch to floating chat demo"
      >
        💬 Floating Chat Demo
      </button>
      <div className="app">
        <header className="app-header">
          <h1>🤖 Copilot Studio Web Chat</h1>
          <p>Microsoft Bot Framework Integration POC - TypeScript Version</p>
        </header>

        <main className="app-main">
          <WebChatComponent />
        </main>

        <footer className="app-footer">
          <p>Built with React + TypeScript + Vite + Bot Framework Web Chat</p>
        </footer>
      </div>
    </>
  )
}

export default App
