import React from 'react'
import WebChatComponent from './components/WebChatComponent'
import './App.css'

/**
 * Main App component
 * Provides the layout structure for the Copilot Studio WebChat application
 */
const App: React.FC = () => {
  return (
    <div className="app">
      <header className="app-header">
        <h1>🤖 Copilot Studio Web Chat</h1>
        <p>Microsoft Bot Framework Integration POC - TypeScript Version</p>
      </header>

      <main className="app-main">
        <WebChatComponent />
      </main>

      <footer className="app-footer">
        <p>Built with React + TypeScript + Vite + Bot Framework Web Chat</p>
      </footer>
    </div>
  )
}

export default App
