import React from 'react'
import { theme } from '../theme'
import { FloatingChatLauncher } from './FloatingChatLauncher'

/**
 * DemoPage - Demonstration page showcasing the floating chat launcher
 * Simulates a real webpage with content to show how the chat widget integrates
 */
export const DemoPage: React.FC = () => {
  const pageStyles: React.CSSProperties = {
    fontFamily: theme.fonts.families.primary,
    lineHeight: 1.6,
    color: theme.colors.neutral800,
    margin: 0,
    padding: 0,
    minHeight: '100vh',
    backgroundColor: theme.colors.neutral50,
  }

  const headerStyles: React.CSSProperties = {
    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
    color: theme.colors.neutralWhite,
    padding: `${theme.spacing[6]} ${theme.spacing[4]}`,
    textAlign: 'center',
    boxShadow: theme.shadows.medium,
  }

  const containerStyles: React.CSSProperties = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: `${theme.spacing[8]} ${theme.spacing[4]}`,
  }

  const sectionStyles: React.CSSProperties = {
    marginBottom: theme.spacing[8],
    backgroundColor: theme.colors.neutralWhite,
    padding: theme.spacing[6],
    borderRadius: '16px',
    boxShadow: theme.shadows.small,
    border: `1px solid ${theme.colors.neutral200}`,
  }

  const headingStyles: React.CSSProperties = {
    color: theme.colors.brandRed,
    marginBottom: theme.spacing[4],
    fontSize: theme.fonts.sizes['2xl'],
    fontWeight: theme.fonts.weights.bold,
  }

  const subheadingStyles: React.CSSProperties = {
    color: theme.colors.neutral700,
    marginBottom: theme.spacing[3],
    fontSize: theme.fonts.sizes.xl,
    fontWeight: theme.fonts.weights.semibold,
  }

  const paragraphStyles: React.CSSProperties = {
    marginBottom: theme.spacing[4],
    fontSize: theme.fonts.sizes.base,
    lineHeight: 1.7,
  }

  const listStyles: React.CSSProperties = {
    paddingLeft: theme.spacing[6],
    marginBottom: theme.spacing[4],
  }

  const listItemStyles: React.CSSProperties = {
    marginBottom: theme.spacing[2],
    fontSize: theme.fonts.sizes.base,
  }

  const footerStyles: React.CSSProperties = {
    backgroundColor: theme.colors.neutral800,
    color: theme.colors.neutralWhite,
    padding: `${theme.spacing[8]} ${theme.spacing[4]}`,
    textAlign: 'center',
    marginTop: theme.spacing[8],
  }

  const buttonStyles: React.CSSProperties = {
    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
    color: theme.colors.neutralWhite,
    border: 'none',
    padding: `${theme.spacing[3]} ${theme.spacing[6]}`,
    borderRadius: '8px',
    fontSize: theme.fonts.sizes.base,
    fontWeight: theme.fonts.weights.medium,
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
    marginRight: theme.spacing[3],
    marginBottom: theme.spacing[3],
  }

  return (
    <div style={pageStyles}>
      {/* Header */}
      <header style={headerStyles}>
        <h1 style={{ margin: 0, fontSize: theme.fonts.sizes['4xl'], fontWeight: theme.fonts.weights.bold }}>
          Copilot Studio WebChat Demo
        </h1>
        <p style={{ margin: `${theme.spacing[4]} 0 0 0`, fontSize: theme.fonts.sizes.lg, opacity: 0.9 }}>
          Experience our floating chat widget in action
        </p>
      </header>

      {/* Main Content */}
      <main style={containerStyles}>
        {/* Introduction Section */}
        <section style={sectionStyles}>
          <h2 style={headingStyles}>Welcome to Our Platform</h2>
          <p style={paragraphStyles}>
            This demo page showcases our floating chat launcher implementation. The chat widget is positioned 
            in the bottom-right corner and provides a seamless way to interact with our AI assistant.
          </p>
          <p style={paragraphStyles}>
            Try clicking the chat button to open the conversation window. The interface is fully responsive 
            and works great on both desktop and mobile devices.
          </p>
          <div>
            <button style={buttonStyles}>Get Started</button>
            <button style={{...buttonStyles, background: 'transparent', color: theme.colors.brandRed, border: `2px solid ${theme.colors.brandRed}`}}>
              Learn More
            </button>
          </div>
        </section>

        {/* Features Section */}
        <section style={sectionStyles}>
          <h2 style={headingStyles}>Key Features</h2>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: theme.spacing[6] }}>
            <div>
              <h3 style={subheadingStyles}>🚀 Easy Integration</h3>
              <p style={paragraphStyles}>
                Simple to integrate into any website with minimal configuration required.
              </p>
            </div>
            <div>
              <h3 style={subheadingStyles}>🎨 Customizable Design</h3>
              <p style={paragraphStyles}>
                Fully customizable appearance that matches your brand colors and style.
              </p>
            </div>
            <div>
              <h3 style={subheadingStyles}>📱 Mobile Responsive</h3>
              <p style={paragraphStyles}>
                Optimized for mobile devices with touch-friendly interactions.
              </p>
            </div>
            <div>
              <h3 style={subheadingStyles}>♿ Accessible</h3>
              <p style={paragraphStyles}>
                Built with accessibility in mind, supporting keyboard navigation and screen readers.
              </p>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section style={sectionStyles}>
          <h2 style={headingStyles}>How It Works</h2>
          <ol style={listStyles}>
            <li style={listItemStyles}>
              <strong>Click the chat button</strong> - Located in the bottom-right corner of the page
            </li>
            <li style={listItemStyles}>
              <strong>Chat window opens</strong> - A floating window appears with smooth animations
            </li>
            <li style={listItemStyles}>
              <strong>Start conversing</strong> - Type your message and interact with the AI assistant
            </li>
            <li style={listItemStyles}>
              <strong>Close when done</strong> - Click the × button or press Escape to close
            </li>
          </ol>
        </section>

        {/* Technical Details Section */}
        <section style={sectionStyles}>
          <h2 style={headingStyles}>Technical Implementation</h2>
          <p style={paragraphStyles}>
            This floating chat widget is built with modern web technologies:
          </p>
          <ul style={listStyles}>
            <li style={listItemStyles}>React with TypeScript for type safety</li>
            <li style={listItemStyles}>Microsoft Bot Framework WebChat integration</li>
            <li style={listItemStyles}>Custom theme system with brand colors</li>
            <li style={listItemStyles}>CSS animations and transitions</li>
            <li style={listItemStyles}>Responsive design for all devices</li>
            <li style={listItemStyles}>Accessibility features (ARIA labels, keyboard navigation)</li>
          </ul>
        </section>
      </main>

      {/* Footer */}
      <footer style={footerStyles}>
        <p style={{ margin: 0, fontSize: theme.fonts.sizes.base }}>
          © 2024 Copilot Studio WebChat Demo. Built with React and TypeScript.
        </p>
        <p style={{ margin: `${theme.spacing[2]} 0 0 0`, fontSize: theme.fonts.sizes.sm, opacity: 0.8 }}>
          Try the floating chat widget in the bottom-right corner!
        </p>
      </footer>

      {/* Floating Chat Launcher */}
      <FloatingChatLauncher
        icon="💬"
        buttonSize="lg"
        windowWidth="400px"
        windowHeight="600px"
        showOnMobile={true}
        onStateChange={(isOpen) => {
          console.log('Chat state changed:', isOpen ? 'opened' : 'closed')
        }}
      />
    </div>
  )
}

export default DemoPage
