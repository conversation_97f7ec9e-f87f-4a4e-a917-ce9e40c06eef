import React, { useState, useCallback, useEffect } from 'react'
import { ChatLauncherButton } from './ui/ChatLauncherButton'
import { FloatingChatWindow } from './ui/FloatingChatWindow'
import WebChatComponent from './WebChatComponent'

interface FloatingChatLauncherProps {
  /** Custom icon for the launcher button */
  icon?: string
  /** Button size */
  buttonSize?: 'sm' | 'md' | 'lg'
  /** Chat window dimensions */
  windowWidth?: string
  windowHeight?: string
  /** Position configuration for button and window */
  position?: {
    bottom?: string
    right?: string
    left?: string
    top?: string
  }
  /** Whether to show on mobile devices */
  showOnMobile?: boolean
  /** Animation duration in milliseconds */
  animationDuration?: number
  /** Initial state of the chat (open/closed) */
  initialOpen?: boolean
  /** Callback when chat state changes */
  onStateChange?: (isOpen: boolean) => void
}

/**
 * FloatingChatLauncher - Complete floating chat widget
 * Combines the launcher button, floating window, and WebChat component
 * Provides a modern chat widget experience similar to customer support systems
 */
export const FloatingChatLauncher: React.FC<FloatingChatLauncherProps> = ({
  icon = '💬',
  buttonSize = 'lg',
  windowWidth = '400px',
  windowHeight = '600px',
  position = { bottom: '20px', right: '20px' },
  showOnMobile = true,
  animationDuration = 300,
  initialOpen = false,
  onStateChange
}) => {
  const [isOpen, setIsOpen] = useState(initialOpen)

  // Handle chat state changes
  const handleToggleChat = useCallback(() => {
    const newState = !isOpen
    setIsOpen(newState)
    onStateChange?.(newState)
  }, [isOpen, onStateChange])

  const handleCloseChat = useCallback(() => {
    setIsOpen(false)
    onStateChange?.(false)
  }, [onStateChange])

  // Handle escape key globally
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleCloseChat()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, handleCloseChat])

  // Calculate window position based on button position
  const getWindowPosition = () => {
    const buttonBottom = position.bottom || '20px'
    const buttonRight = position.right || '20px'
    
    // Parse numeric values
    const bottomValue = parseInt(buttonBottom.replace('px', ''))
    const rightValue = parseInt(buttonRight.replace('px', ''))
    
    // Position window above the button with some spacing
    const windowBottom = `${bottomValue + 80}px`
    const windowRight = `${rightValue}px`
    
    return {
      bottom: windowBottom,
      right: windowRight,
      left: position.left,
      top: position.top
    }
  }

  const windowPosition = getWindowPosition()

  return (
    <>
      {/* Chat Launcher Button */}
      <ChatLauncherButton
        isOpen={isOpen}
        onClick={handleToggleChat}
        icon={icon}
        size={buttonSize}
        position={position}
      />

      {/* Floating Chat Window */}
      <FloatingChatWindow
        isOpen={isOpen}
        width={windowWidth}
        height={windowHeight}
        position={windowPosition}
        animationDuration={animationDuration}
        showOnMobile={showOnMobile}
      >
        <WebChatComponent
          isFloating={true}
          onClose={handleCloseChat}
        />
      </FloatingChatWindow>
    </>
  )
}

export default FloatingChatLauncher
