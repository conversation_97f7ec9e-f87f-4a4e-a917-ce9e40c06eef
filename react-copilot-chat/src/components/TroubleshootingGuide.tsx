import React from 'react'
import { theme } from '../theme'

/**
 * TroubleshootingGuide - Comprehensive guide for fixing Direct Line connection issues
 */
export const TroubleshootingGuide: React.FC = () => {
  const containerStyles: React.CSSProperties = {
    padding: theme.spacing[6],
    fontFamily: theme.fonts.families.primary,
    maxWidth: '800px',
    margin: '0 auto',
    backgroundColor: theme.colors.neutralWhite,
    borderRadius: '12px',
    boxShadow: theme.shadows.medium,
    border: `1px solid ${theme.colors.neutral200}`,
  }

  const sectionStyles: React.CSSProperties = {
    marginBottom: theme.spacing[6],
    padding: theme.spacing[4],
    backgroundColor: theme.colors.neutral50,
    borderRadius: '8px',
    border: `1px solid ${theme.colors.neutral200}`,
  }

  const headingStyles: React.CSSProperties = {
    color: theme.colors.brandRed,
    fontSize: theme.fonts.sizes.xl,
    fontWeight: theme.fonts.weights.bold,
    marginBottom: theme.spacing[3],
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing[2],
  }

  const stepStyles: React.CSSProperties = {
    marginBottom: theme.spacing[3],
    padding: theme.spacing[3],
    backgroundColor: theme.colors.neutralWhite,
    borderRadius: '6px',
    border: `1px solid ${theme.colors.neutral200}`,
  }

  const codeStyles: React.CSSProperties = {
    backgroundColor: theme.colors.neutral100,
    padding: theme.spacing[2],
    borderRadius: '4px',
    fontFamily: 'monospace',
    fontSize: theme.fonts.sizes.sm,
    border: `1px solid ${theme.colors.neutral300}`,
    overflow: 'auto',
  }

  return (
    <div style={containerStyles}>
      <h1 style={{
        ...headingStyles,
        fontSize: theme.fonts.sizes['2xl'],
        textAlign: 'center',
        marginBottom: theme.spacing[6],
      }}>
        🛠️ Direct Line Connection Troubleshooting Guide
      </h1>

      {/* Common Issues */}
      <section style={sectionStyles}>
        <h2 style={headingStyles}>
          🚨 Common Issues & Solutions
        </h2>
        
        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandRed, marginBottom: theme.spacing[2] }}>
            ❌ "Failed to connect to bot"
          </h3>
          <p><strong>Cause:</strong> Expired or invalid Direct Line token</p>
          <p><strong>Solution:</strong></p>
          <ol style={{ paddingLeft: theme.spacing[4] }}>
            <li>Go to Azure Portal → Your Bot → Channels → Direct Line</li>
            <li>Generate a new token (not secret)</li>
            <li>Copy the token and update your .env file</li>
            <li>Restart your development server</li>
          </ol>
        </div>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandRed, marginBottom: theme.spacing[2] }}>
            ❌ "Token format invalid"
          </h3>
          <p><strong>Cause:</strong> Incorrect token format or copied secret instead of token</p>
          <p><strong>Solution:</strong></p>
          <ul style={{ paddingLeft: theme.spacing[4] }}>
            <li>Ensure you copied the <strong>token</strong>, not the secret</li>
            <li>Token should be very long (200+ characters)</li>
            <li>Remove any extra spaces or line breaks</li>
          </ul>
        </div>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandRed, marginBottom: theme.spacing[2] }}>
            ❌ "Bot not responding"
          </h3>
          <p><strong>Cause:</strong> Bot service is not running or misconfigured</p>
          <p><strong>Solution:</strong></p>
          <ul style={{ paddingLeft: theme.spacing[4] }}>
            <li>Check if your bot is deployed and running in Azure</li>
            <li>Verify the bot endpoint URL is correct</li>
            <li>Test the bot in the Azure portal's "Test in Web Chat"</li>
          </ul>
        </div>
      </section>

      {/* Step-by-step Setup */}
      <section style={sectionStyles}>
        <h2 style={headingStyles}>
          📋 Step-by-Step Setup Guide
        </h2>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandBlue, marginBottom: theme.spacing[2] }}>
            Step 1: Create/Deploy Your Bot
          </h3>
          <ol style={{ paddingLeft: theme.spacing[4] }}>
            <li>Create a bot in Azure Bot Service or Copilot Studio</li>
            <li>Deploy your bot to Azure (if using custom bot)</li>
            <li>Test the bot in Azure portal to ensure it's working</li>
          </ol>
        </div>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandBlue, marginBottom: theme.spacing[2] }}>
            Step 2: Configure Direct Line Channel
          </h3>
          <ol style={{ paddingLeft: theme.spacing[4] }}>
            <li>Go to Azure Portal → Your Bot → Channels</li>
            <li>Click on "Direct Line" channel</li>
            <li>Add a new site (give it any name)</li>
            <li>Copy one of the generated tokens (not the secret)</li>
          </ol>
        </div>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandBlue, marginBottom: theme.spacing[2] }}>
            Step 3: Update Environment Variables
          </h3>
          <p>Update your <code>.env</code> file:</p>
          <div style={codeStyles}>
            VITE_DIRECT_LINE_TOKEN=your_very_long_token_here<br/>
            VITE_BOT_NAME=Your Bot Name<br/>
            VITE_WELCOME_MESSAGE=Hello! How can I help you?
          </div>
        </div>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandBlue, marginBottom: theme.spacing[2] }}>
            Step 4: Restart Development Server
          </h3>
          <p>After updating the .env file:</p>
          <div style={codeStyles}>
            npm run dev
          </div>
        </div>
      </section>

      {/* Testing */}
      <section style={sectionStyles}>
        <h2 style={headingStyles}>
          🧪 Testing Your Setup
        </h2>
        
        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandGreen, marginBottom: theme.spacing[2] }}>
            Use the Debug Mode
          </h3>
          <p>Click the "🧪 Debug Mode" button in the top-left corner to:</p>
          <ul style={{ paddingLeft: theme.spacing[4] }}>
            <li>Validate your token format</li>
            <li>Test Direct Line API connection</li>
            <li>Get detailed error information</li>
          </ul>
        </div>

        <div style={stepStyles}>
          <h3 style={{ color: theme.colors.brandGreen, marginBottom: theme.spacing[2] }}>
            Check Browser Console
          </h3>
          <p>Open browser developer tools (F12) and look for:</p>
          <ul style={{ paddingLeft: theme.spacing[4] }}>
            <li>🚀 Initializing WebChat messages</li>
            <li>🔄 Connection status updates</li>
            <li>❌ Error messages with details</li>
          </ul>
        </div>
      </section>

      {/* Contact */}
      <section style={{
        ...sectionStyles,
        backgroundColor: theme.colors.brandLightRed,
        border: `1px solid ${theme.colors.brandRed}`,
      }}>
        <h2 style={headingStyles}>
          💡 Still Having Issues?
        </h2>
        <p style={{ marginBottom: theme.spacing[3] }}>
          If you're still experiencing problems after following this guide:
        </p>
        <ul style={{ paddingLeft: theme.spacing[4] }}>
          <li>Check the browser console for detailed error messages</li>
          <li>Verify your bot is working in Azure portal's "Test in Web Chat"</li>
          <li>Try generating a fresh Direct Line token</li>
          <li>Ensure your bot service is running and accessible</li>
        </ul>
      </section>
    </div>
  )
}

export default TroubleshootingGuide
