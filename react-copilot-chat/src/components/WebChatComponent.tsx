import React, { useState, useEffect, useMemo } from 'react'
import ReactWeb<PERSON><PERSON>, { createDirectLine, createStyleSet } from 'botframework-webchat'
import { Card, StatusIndicator, LoadingIndicator, ErrorIndicator } from '@/components/ui'
import { theme, createWebChatStyleSet } from '@/theme'
import { DirectLineOptions } from '@/types'
import { validateTokenFormat, getTokenTroubleshootingSteps } from '../utils/tokenValidator'
import { runBotDiagnostics, printDiagnosticResults } from '../utils/botDiagnostics'
// import { activityMiddleware } from './richCards/RichCardConfig'
import { getResponsiveCardStyles } from './richCards/RichCardStyles'

interface WebChatComponentProps {
  /** Whether the chat is embedded in a floating window */
  isFloating?: boolean
  /** Callback when close button is clicked (for floating mode) */
  onClose?: () => void
}

/**
 * WebChatComponent - Main component for Microsoft Bot Framework WebChat integration
 * Handles DirectLine connection, error states, and provides a secure chat interface
 * Only works with environment-configured Direct Line tokens
 */
const WebChatComponent: React.FC<WebChatComponentProps> = ({
  isFloating = false,
  onClose
}) => {
  // Get configuration from environment variables
  const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
  const botName = import.meta.env.VITE_BOT_NAME || 'Copilot Assistant'

  // Component state
  const [directLine, setDirectLine] = useState<unknown | null>(null)
  const [error, setError] = useState<string>('')
  const [isInitializing, setIsInitializing] = useState<boolean>(true)
  const [connectionStatus, setConnectionStatus] = useState<string>('Initializing')
  const [retryCount, setRetryCount] = useState<number>(0)

  // Comprehensive test function to verify DirectLine and bot connectivity
  const testDirectLineConnection = React.useCallback(async () => {
    console.log('🧪 Starting comprehensive DirectLine connection test...')

    try {
      // Step 1: Test DirectLine API
      console.log('📡 Step 1: Testing DirectLine API...')
      const response = await fetch('https://directline.botframework.com/v3/directline/conversations', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${envToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        console.error('❌ DirectLine API test failed:', response.status, response.statusText)
        const errorText = await response.text()
        console.error('Error details:', errorText)
        return null
      }

      const conversationData = await response.json()
      console.log('✅ DirectLine API test successful:', conversationData)

      // Step 2: Test sending a message to the bot
      console.log('📨 Step 2: Testing message sending to bot...')
      const messageResponse = await fetch(`https://directline.botframework.com/v3/directline/conversations/${conversationData.conversationId}/activities`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${envToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'message',
          text: 'Hello from test',
          from: {
            id: 'test-user',
            name: 'Test User'
          }
        })
      })

      if (messageResponse.ok) {
        const messageResult = await messageResponse.json()
        console.log('✅ Message sent successfully:', messageResult)
      } else {
        console.error('❌ Failed to send message:', messageResponse.status, messageResponse.statusText)
      }

      // Step 3: Test receiving messages from the bot
      console.log('📥 Step 3: Testing message receiving from bot...')
      const activitiesResponse = await fetch(`https://directline.botframework.com/v3/directline/conversations/${conversationData.conversationId}/activities`, {
        headers: {
          'Authorization': `Bearer ${envToken}`
        }
      })

      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json()
        console.log('✅ Activities retrieved:', activitiesData)

        if (activitiesData.activities && activitiesData.activities.length > 0) {
          console.log('📋 Bot activities found:', activitiesData.activities.length)
          activitiesData.activities.forEach((activity: unknown, index: number) => {
            const act = activity as Record<string, unknown>
            console.log(`Activity ${index + 1}:`, {
              type: act.type,
              from: act.from,
              text: act.text
            })
          })
        } else {
          console.log('⚠️ No activities received from bot - bot may not be responding')
        }
      } else {
        console.error('❌ Failed to retrieve activities:', activitiesResponse.status, activitiesResponse.statusText)
      }

      return conversationData

    } catch (error) {
      console.error('❌ Comprehensive DirectLine test error:', error)
      return null
    }
  }, [envToken])

  // Run comprehensive diagnostics
  const runFullDiagnostics = React.useCallback(async () => {
    console.log('🔍 Running full bot diagnostics...')
    const diagnostics = await runBotDiagnostics(envToken)
    printDiagnosticResults(diagnostics)
    return diagnostics
  }, [envToken])

  // Expose test functions to window for manual debugging
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const windowObj = window as unknown as Record<string, unknown>
      windowObj.testDirectLineConnection = testDirectLineConnection
      windowObj.runFullDiagnostics = runFullDiagnostics
      console.log('🔧 Debug functions available:')
      console.log('  - window.testDirectLineConnection()')
      console.log('  - window.runFullDiagnostics()')
    }
  }, [testDirectLineConnection, runFullDiagnostics])

  // Initialize DirectLine connection on component mount
  useEffect(() => {
    const initializeWebChat = async (): Promise<void> => {
      console.log('🚀 Initializing WebChat...', { isFloating, envToken: envToken ? 'Token present' : 'No token' })

      // Check if token is available and valid
      if (!envToken || envToken === 'your_direct_line_token_here') {
        const errorMsg = 'Direct Line token not configured. Please set VITE_DIRECT_LINE_TOKEN in your .env file.'
        console.error('❌ Token validation failed:', errorMsg)
        setError(errorMsg)
        setIsInitializing(false)
        return
      }

      // Validate token format using utility function
      const tokenValidation = validateTokenFormat(envToken)
      if (!tokenValidation.isValid) {
        const errorMsg = `Invalid Direct Line token: ${tokenValidation.error}`
        const troubleshootingSteps = getTokenTroubleshootingSteps(tokenValidation.error || '')
        console.error('❌ Token format validation failed:', {
          error: errorMsg,
          troubleshooting: troubleshootingSteps,
          details: tokenValidation.details
        })
        setError(`${errorMsg}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
        setIsInitializing(false)
        return
      }

      try {
        console.log('🔗 Creating DirectLine connection...', {
          tokenLength: envToken.length,
          mode: isFloating ? 'floating' : 'fullpage'
        })

        // Create DirectLine connection with proper options
        const directLineOptions: DirectLineOptions = {
          token: envToken.trim(),
          pollingInterval: 1000,
        }

        console.log('🔧 DirectLine options:', {
          tokenLength: directLineOptions.token?.length,
          pollingInterval: directLineOptions.pollingInterval,
          mode: isFloating ? 'floating' : 'fullpage'
        })

        const dl = createDirectLine(directLineOptions)
        console.log('🔗 DirectLine instance created:', dl)
        console.log('🔗 DirectLine properties:', {
          connectionStatus$: !!dl.connectionStatus$,
          activity$: !!dl.activity$,
          postActivity: typeof dl.postActivity,
          hasToken: !!directLineOptions.token
        })

        // Set up connection status monitoring with proper typing
        dl.connectionStatus$.subscribe({
          next: (connectionStatus: number) => {
            const statusMap = {
              0: 'Uninitialized',
              1: 'Connecting',
              2: 'Online',
              3: 'ExpiredToken',
              4: 'FailedToConnect',
              5: 'Ended'
            }
            const statusName = statusMap[connectionStatus as keyof typeof statusMap] || 'Unknown'
            console.log(`🔄 Connection status changed: ${connectionStatus} (${statusName})`, {
              isFloating,
              timestamp: new Date().toISOString(),
              previousStatus: connectionStatus
            })
            setConnectionStatus(statusName)

            if (connectionStatus === 2) { // Online
              console.log('✅ WebChat connected successfully!', { mode: isFloating ? 'floating' : 'fullpage' })
              setError('') // Clear any previous errors
              setRetryCount(0) // Reset retry count on successful connection
            } else if (connectionStatus === 3) { // ExpiredToken
              const errorMsg = 'Direct Line token has expired. Please generate a new token.'
              const troubleshootingSteps = getTokenTroubleshootingSteps('expired')
              console.error('❌ Token expired:', errorMsg, { troubleshooting: troubleshootingSteps })
              setError(`${errorMsg}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
            } else if (connectionStatus === 4) { // FailedToConnect
              const errorMsg = 'Failed to connect to bot. Please check your Direct Line token and bot service.'
              const troubleshootingSteps = getTokenTroubleshootingSteps('404')
              console.error('❌ Connection failed:', errorMsg, { troubleshooting: troubleshootingSteps })
              setError(`${errorMsg}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
            } else if (connectionStatus === 5) { // Ended
              console.warn('⚠️ Connection ended')
            }
          },
          error: (connectionError: Error) => {
            console.error('❌ Connection error:', connectionError, { isFloating })
            setError(`Connection error: ${connectionError.message}`)
          }
        })

        // Monitor activities for debugging
        dl.activity$.subscribe({
          next: (activity: unknown) => {
            const activityObj = activity as Record<string, unknown>
            console.log('📨 Activity received:', {
              type: activityObj.type,
              from: (activityObj.from as Record<string, unknown>)?.name || (activityObj.from as Record<string, unknown>)?.id,
              text: typeof activityObj.text === 'string' ?
                activityObj.text.substring(0, 100) + (activityObj.text.length > 100 ? '...' : '') :
                'No text',
              timestamp: activityObj.timestamp,
              hasChannelData: !!activityObj.channelData
            })
          },
          error: (activityError: Error) => {
            console.error('❌ Activity stream error:', activityError)
          }
        })

        setDirectLine(dl)
        setIsInitializing(false)
        console.log('✅ DirectLine setup completed successfully')

        // Test sending a message after a short delay to verify bot connectivity
        setTimeout(() => {
          console.log('🧪 Testing bot connectivity by sending a test message...')
          dl.postActivity({
            type: 'message',
            text: 'Hello',
            from: { id: 'user', name: 'User' }
          }).subscribe({
            next: (id: string) => {
              console.log('✅ Test message sent successfully, activity ID:', id)
            },
            error: (postError: Error) => {
              console.error('❌ Failed to send test message:', postError)
            }
          })
        }, 2000)

      } catch (initError) {
        console.error('❌ Error initializing WebChat:', initError)
        const errorMessage = initError instanceof Error ? initError.message : 'Unknown error occurred'
        const troubleshootingSteps = getTokenTroubleshootingSteps(errorMessage)
        setError(`Error initializing WebChat: ${errorMessage}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
        setIsInitializing(false)
        setConnectionStatus('Error')
      }
    }

    initializeWebChat()
  }, [envToken, isFloating])

  // Retry connection function
  const retryConnection = () => {
    setRetryCount(prev => prev + 1)
    setError('')
    setIsInitializing(true)
    setConnectionStatus('Retrying...')

    // Re-trigger the useEffect by updating a dependency
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  // Create style set using the centralized theme system with rich card support
  const styleSet = useMemo(() => {
    const baseStyleSet = createStyleSet(createWebChatStyleSet())

    // Enhance with rich card styles for responsive design
    const responsiveCardStyles = getResponsiveCardStyles(isFloating)

    return {
      ...baseStyleSet,
      // Enhance adaptive card renderer
      adaptiveCardRenderer: {
        ...baseStyleSet.adaptiveCardRenderer,
        ...responsiveCardStyles,
      },
      // Enhance activities container
      activities: {
        ...baseStyleSet.activities,
        fontFamily: theme.fonts.families.primary,
      },
      // Enhance attachment styles
      audioCardAttachment: {
        ...baseStyleSet.audioCardAttachment,
        borderRadius: '8px',
        margin: `${theme.spacing[2]} 0`,
      },
      // Enhance general attachment styles
      attachment: {
        borderRadius: '8px',
        margin: `${theme.spacing[2]} 0`,
        boxShadow: theme.shadows.small,
        border: `1px solid ${theme.colors.neutral200}`,
        overflow: 'hidden',
      },
    }
  }, [isFloating])

  // Activity middleware for rich card rendering (temporarily disabled for debugging)
  // const activityMiddlewareConfig = useMemo(() => [activityMiddleware], [])

  // Container styles using theme tokens
  const containerStyles: React.CSSProperties = {
    ...theme.components.webChat.container,
  }

  const configPanelStyles: React.CSSProperties = {
    ...theme.components.webChat.configPanel,
  }

  const chatWrapperStyles: React.CSSProperties = {
    ...theme.components.webChat.chatWrapper,
  }

  const chatStyles: React.CSSProperties = {
    ...theme.components.webChat.chat,
  }

  // Render loading state
  if (isInitializing) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <LoadingIndicator
            title="Initializing WebChat..."
            description={`Connecting to ${botName}`}
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Initializing chat...
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                Please wait while we connect to the bot
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Render error state
  if (error) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <ErrorIndicator
            title="Configuration Error"
            description="WebChat cannot be initialized"
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.error,
              padding: theme.spacing[5],
              maxWidth: '80%',
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Configuration Error
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                lineHeight: theme.fonts.lineHeights.normal,
                display: 'block',
                wordBreak: 'break-word',
                marginBottom: theme.spacing[4],
              }}>
                {error}
              </small>
              <div style={{ marginTop: theme.spacing[4] }}>
                <button
                  onClick={retryConnection}
                  style={{
                    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
                    color: theme.colors.neutralWhite,
                    border: 'none',
                    padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
                    borderRadius: '6px',
                    fontSize: theme.fonts.sizes.sm,
                    fontWeight: theme.fonts.weights.medium,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    marginRight: theme.spacing[2],
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)'
                    e.currentTarget.style.boxShadow = theme.shadows.medium
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                >
                  🔄 Retry Connection {retryCount > 0 && `(${retryCount})`}
                </button>
                <small style={{
                  fontSize: theme.fonts.sizes.xs,
                  color: theme.colors.neutral600,
                  display: 'block',
                  marginTop: theme.spacing[2],
                }}>
                  Status: {connectionStatus}
                </small>
              </div>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Render connection issue state
  if (!directLine) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <StatusIndicator
            status="warning"
            title="Connection Issue"
            description="DirectLine not available"
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Connection Issue
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                DirectLine connection not established
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Main WebChat render - successful connection
  return (
    <Card variant="elevated" style={containerStyles}>
      {isFloating && onClose && (
        <div style={{
          position: 'absolute',
          top: theme.spacing[3],
          right: theme.spacing[3],
          zIndex: 1000,
        }}>
          <button
            onClick={onClose}
            style={{
              background: 'transparent',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              color: theme.colors.neutral600,
              padding: theme.spacing[1],
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
              transition: 'all 0.2s ease-in-out',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = theme.colors.neutral100
              e.currentTarget.style.color = theme.colors.neutral800
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = theme.colors.neutral600
            }}
            aria-label="Close chat"
            title="Close chat"
          >
            ×
          </button>
        </div>
      )}
      {!isFloating && (
        <div style={configPanelStyles}>
          <StatusIndicator
            status="success"
            title={`Connected to ${botName}`}
            description="Using environment token"
          />
        </div>
      )}
      <div style={chatWrapperStyles}>
        <div style={chatStyles}>
          {directLine ? (
            <div style={{ position: 'relative', height: '100%' }}>
              <ReactWebChat
                directLine={directLine}
                styleSet={styleSet}
                userID={`user-${Math.random().toString(36).substring(2, 11)}`}
                locale="en-US"
              />

              {/* Debug overlay */}
              <div style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'rgba(0,0,0,0.7)',
                color: 'white',
                padding: '5px 10px',
                borderRadius: '4px',
                fontSize: '12px',
                zIndex: 1000,
                pointerEvents: 'none'
              }}>
                Status: {connectionStatus}
              </div>
            </div>
          ) : (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Establishing Connection...
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                Status: {connectionStatus}
              </small>

              {/* Debug info */}
              <div style={{
                marginTop: theme.spacing[4],
                padding: theme.spacing[3],
                backgroundColor: theme.colors.neutral100,
                borderRadius: '6px',
                fontSize: theme.fonts.sizes.xs,
                textAlign: 'left'
              }}>
                <strong>Debug Info:</strong><br />
                Token: {envToken ? 'Present' : 'Missing'}<br />
                Initializing: {isInitializing ? 'Yes' : 'No'}<br />
                DirectLine: {directLine ? 'Created' : 'Not created'}<br />
                Error: {error || 'None'}

                <div style={{ marginTop: theme.spacing[2] }}>
                  <button
                    onClick={runFullDiagnostics}
                    style={{
                      padding: `${theme.spacing[1]} ${theme.spacing[2]}`,
                      backgroundColor: theme.colors.brandRed,
                      color: theme.colors.neutralWhite,
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: theme.fonts.sizes.xs,
                      cursor: 'pointer',
                      marginRight: theme.spacing[2]
                    }}
                  >
                    🔍 Run Diagnostics
                  </button>

                  <button
                    onClick={testDirectLineConnection}
                    style={{
                      padding: `${theme.spacing[1]} ${theme.spacing[2]}`,
                      backgroundColor: theme.colors.brandOrange,
                      color: theme.colors.neutralWhite,
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: theme.fonts.sizes.xs,
                      cursor: 'pointer'
                    }}
                  >
                    🧪 Test Connection
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  )
}

export default WebChatComponent