import React, { useState, useEffect } from 'react'
import React<PERSON>eb<PERSON><PERSON>, { createDirectLine, createStyleSet } from 'botframework-webchat'
import { Card, StatusIndicator, LoadingIndicator, ErrorIndicator } from '@/components/ui'
import { theme, createWebChatStyleSet } from '@/theme'
import { DirectLineOptions } from '@/types'

/**
 * WebChatComponent - Main component for Microsoft Bot Framework WebChat integration
 * Handles DirectLine connection, error states, and provides a secure chat interface
 * Only works with environment-configured Direct Line tokens
 */
const WebChatComponent: React.FC = () => {
  // Get configuration from environment variables
  const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
  const botName = import.meta.env.VITE_BOT_NAME || 'Copilot Assistant'

  // Component state
  const [directLine, setDirectLine] = useState<any | null>(null)
  const [error, setError] = useState<string>('')
  const [isInitializing, setIsInitializing] = useState<boolean>(true)

  // Initialize DirectLine connection on component mount
  useEffect(() => {
    const initializeWebChat = async (): Promise<void> => {
      console.log('🚀 Initializing WebChat...')

      // Check if token is available and valid
      if (!envToken || envToken === 'your_direct_line_token_here') {
        setError('Direct Line token not configured. Please set VITE_DIRECT_LINE_TOKEN in your .env file.')
        setIsInitializing(false)
        return
      }

      try {
        console.log('🔗 Creating DirectLine connection...')

        // Create DirectLine connection with proper options
        const directLineOptions: DirectLineOptions = {
          token: envToken.trim(),
          pollingInterval: 1000,
        }

        const dl = createDirectLine(directLineOptions)

        // Set up connection status monitoring with proper typing
        dl.connectionStatus$.subscribe({
          next: (connectionStatus: number) => {
            console.log('Connection status:', connectionStatus)
            if (connectionStatus === 2) { // Online
              console.log('✅ WebChat connected successfully!')
              setError('') // Clear any previous errors
            } else if (connectionStatus === 4) { // FailedToConnect
              setError('Failed to connect to bot. Please check your Direct Line token.')
            }
          },
          error: (connectionError: Error) => {
            console.error('❌ Connection error:', connectionError)
            setError(`Connection error: ${connectionError.message}`)
          }
        })

        setDirectLine(dl)
        setIsInitializing(false)

      } catch (initError) {
        console.error('❌ Error initializing WebChat:', initError)
        const errorMessage = initError instanceof Error ? initError.message : 'Unknown error occurred'
        setError(`Error initializing WebChat: ${errorMessage}`)
        setIsInitializing(false)
      }
    }

    initializeWebChat()
  }, [envToken])

  // Create style set using the centralized theme system
  const styleSet = createStyleSet(createWebChatStyleSet())

  // Container styles using theme tokens
  const containerStyles: React.CSSProperties = {
    ...theme.components.webChat.container,
  }

  const configPanelStyles: React.CSSProperties = {
    ...theme.components.webChat.configPanel,
  }

  const chatWrapperStyles: React.CSSProperties = {
    ...theme.components.webChat.chatWrapper,
  }

  const chatStyles: React.CSSProperties = {
    ...theme.components.webChat.chat,
  }

  // Render loading state
  if (isInitializing) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <LoadingIndicator
            title="Initializing WebChat..."
            description={`Connecting to ${botName}`}
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Initializing chat...
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                Please wait while we connect to the bot
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Render error state
  if (error) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <ErrorIndicator
            title="Configuration Error"
            description="WebChat cannot be initialized"
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.error,
              padding: theme.spacing[5],
              maxWidth: '80%',
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Configuration Error
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                lineHeight: theme.fonts.lineHeights.normal,
                display: 'block',
                wordBreak: 'break-word',
              }}>
                {error}
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Render connection issue state
  if (!directLine) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <StatusIndicator
            status="warning"
            title="Connection Issue"
            description="DirectLine not available"
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Connection Issue
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                DirectLine connection not established
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Main WebChat render - successful connection
  return (
    <Card variant="elevated" style={containerStyles}>
      <div style={configPanelStyles}>
        <StatusIndicator
          status="success"
          title={`Connected to ${botName}`}
          description="Using environment token"
        />
      </div>
      <div style={chatWrapperStyles}>
        <div style={chatStyles}>
          <ReactWebChat
            directLine={directLine}
            styleSet={styleSet}
            userID={`user-${Math.random().toString(36).substring(2, 11)}`}
            locale="en-US"
          />
        </div>
      </div>
    </Card>
  )
}

export default WebChatComponent