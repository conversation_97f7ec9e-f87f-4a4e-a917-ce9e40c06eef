.webchat-container {
  width: 100%;
  max-width: 900px;
  height: 700px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-panel {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.token-input-group {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.env-token-status {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.token-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.token-indicator {
  font-size: 24px;
  color: #28a745;
}

.token-details p {
  margin: 0;
  font-weight: 600;
  color: #2F2D2D;
  font-size: 14px;
}

.token-details small {
  color: #716A6A;
  font-size: 12px;
}

.token-input {
  flex: 1;
  min-width: 300px;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.token-input:focus {
  outline: none;
  border-color: #0078d4;
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.token-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.button-group {
  display: flex;
  gap: 10px;
}

.connect-btn,
.disconnect-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.connect-btn {
  background: #0078d4;
  color: white;
}

.connect-btn:hover:not(:disabled) {
  background: #106ebe;
  transform: translateY(-1px);
}

.connect-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.connect-btn.connecting {
  background: #6c757d;
}

.disconnect-btn {
  background: #28a745;
  color: white;
}

.disconnect-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.error-message {
  margin-top: 15px;
  padding: 12px 16px;
  background: #ffebee;
  color: #d32f2f;
  border-radius: 8px;
  border-left: 4px solid #d32f2f;
  font-size: 14px;
}

.webchat-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.webchat {
  height: 100%;
  width: 100%;
  min-height: 400px;
  position: relative;
}

/* Ensure WebChat components are visible */
.webchat > * {
  height: 100%;
}

/* Force WebChat send box to be visible */
.webchat [data-testid="sendBox"] {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.webchat [data-testid="sendBoxTextBox"] {
  display: block !important;
  visibility: visible !important;
}

.webchat [data-testid="send-button"] {
  display: flex !important;
  visibility: visible !important;
}

/* Status message styling */
.webchat .status {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
  padding: 20px;
}

.webchat .status p {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 500;
}

.webchat .status small {
  font-size: 14px;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .webchat-container {
    height: 80vh;
    margin: 0 10px;
    border-radius: 8px;
  }

  .config-panel {
    padding: 15px;
  }

  .token-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .token-input {
    min-width: auto;
    margin-bottom: 10px;
  }

  .button-group {
    justify-content: center;
  }

  .connect-btn,
  .disconnect-btn {
    flex: 1;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .webchat-container {
    height: 75vh;
    margin: 0 5px;
  }

  .config-panel {
    padding: 10px;
  }

  .token-input {
    padding: 10px 12px;
    font-size: 13px;
  }

  .connect-btn,
  .disconnect-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* Custom Web Chat styling with your brand tokens */

/* Suggested Actions (Buttons) */
.webchat [role="button"][data-testid*="suggested-action"] {
  background-color: #FFF !important;
  border: 2px solid #E5384C !important;
  border-radius: 8px !important;
  color: #E5384C !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  margin: 4px !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
  box-shadow: 0 2px 4px rgba(229, 56, 76, 0.1) !important;
}

.webchat [role="button"][data-testid*="suggested-action"]:hover {
  background-color: #E5384C !important;
  color: #FFF !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(229, 56, 76, 0.3) !important;
  border-color: #D21242 !important;
}

.webchat [role="button"][data-testid*="suggested-action"]:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(229, 56, 76, 0.2) !important;
}

/* Adaptive Cards */
.webchat .ac-adaptiveCard {
  background-color: #FFF !important;
  border: 1px solid #F3F0F0 !important;
  border-radius: 16px !important;
  box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
  padding: 16px !important;
  margin: 8px 0 !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Card Action Buttons */
.webchat .ac-pushButton {
  background-color: #FFF !important;
  border: 2px solid #E5384C !important;
  border-radius: 8px !important;
  color: #E5384C !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  margin: 4px !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
}

.webchat .ac-pushButton:hover {
  background-color: #E5384C !important;
  color: #FFF !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(229, 56, 76, 0.3) !important;
}

/* Hero Cards */
.webchat .ac-container {
  background-color: #FFF !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
}

/* Card Titles */
.webchat .ac-textBlock {
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  color: #2F2D2D !important;
}

/* Card Images */
.webchat .ac-image {
  border-radius: 8px !important;
}

/* Quick Replies */
.webchat [data-testid="quick-reply"] {
  background-color: #FFF !important;
  border: 2px solid #009b65 !important;
  border-radius: 8px !important;
  color: #009b65 !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  margin: 2px !important;
  transition: all 0.2s ease-in-out !important;
}

.webchat [data-testid="quick-reply"]:hover {
  background-color: #009b65 !important;
  color: #FFF !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(0, 155, 101, 0.3) !important;
}

/* Carousel Cards */
.webchat [data-testid="carousel"] {
  padding: 8px 0 !important;
}

.webchat [data-testid="carousel-item"] {
  background-color: #FFF !important;
  border: 1px solid #F3F0F0 !important;
  border-radius: 16px !important;
  box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
  margin: 0 8px !important;
  overflow: hidden !important;
}

/* Send Box Button */
.webchat [data-testid="send-button"] {
  background-color: #E5384C !important;
  border-radius: 8px !important;
  transition: all 0.2s ease-in-out !important;
}

.webchat [data-testid="send-button"]:hover {
  background-color: #D21242 !important;
  transform: scale(1.05) !important;
}

/* Typing Indicator */
.webchat [data-testid="typing-indicator"] {
  color: #716A6A !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Timestamp */
.webchat [data-testid="timestamp"] {
  color: #716A6A !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-size: 12px !important;
}

/* Message bubbles enhancement */
.webchat [data-testid="message-bubble"] {
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  line-height: 1.5 !important;
}

/* Bot message bubbles */
.webchat [data-testid="message-bubble"][data-from="bot"] {
  background-color: #FFF !important;
  border: 1px solid #F3F0F0 !important;
  color: #2F2D2D !important;
}

/* User message bubbles with gradient */
.webchat [data-testid="message-bubble"][data-from="user"] {
  background: linear-gradient(90deg, #E5384C 0%, #EA714F 100%) !important;
  color: #FFF !important;
  border: none !important;
}