/* TypeScript WebChat Component Styles */
/* Using centralized theme system for consistent styling */

/* Global animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Button component styles */
.button {
  transition: all 0.2s ease-in-out;
  animation: fadeIn 0.3s ease-out;
}

.button:hover:not(:disabled) {
  transform: translateY(-1px);
}

.button:active:not(:disabled) {
  transform: translateY(0);
}

/* Card component styles */
.card {
  animation: fadeIn 0.3s ease-out;
}

.card:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease-in-out;
}

/* Status indicator styles */
.status-indicator {
  transition: all 0.2s ease-in-out;
  animation: fadeIn 0.3s ease-out;
}

.status-indicator:hover {
  transform: translateY(-1px);
}

/* WebChat Bot Framework Integration Styles */
/* These styles ensure proper integration with Microsoft Bot Framework WebChat */

/* Force WebChat components to be visible and properly sized */
.webchat > * {
  height: 100% !important;
  width: 100% !important;
}

/* Ensure WebChat send box is always visible */
.webchat [data-testid="sendBox"] {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.webchat [data-testid="sendBoxTextBox"] {
  display: block !important;
  visibility: visible !important;
}

.webchat [data-testid="send-button"] {
  display: flex !important;
  visibility: visible !important;
}

/* Brand-specific WebChat styling using theme colors */
/* Suggested Actions (Buttons) */
.webchat [role="button"][data-testid*="suggested-action"] {
  background-color: #FFF !important;
  border: 2px solid #E5384C !important;
  border-radius: 8px !important;
  color: #E5384C !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  margin: 4px !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
  box-shadow: 0 2px 4px rgba(229, 56, 76, 0.1) !important;
}

.webchat [role="button"][data-testid*="suggested-action"]:hover {
  background-color: #E5384C !important;
  color: #FFF !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(229, 56, 76, 0.3) !important;
  border-color: #D21242 !important;
}

.webchat [role="button"][data-testid*="suggested-action"]:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(229, 56, 76, 0.2) !important;
}

/* Adaptive Cards */
.webchat .ac-adaptiveCard {
  background-color: #FFF !important;
  border: 1px solid #F3F0F0 !important;
  border-radius: 16px !important;
  box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
  padding: 16px !important;
  margin: 8px 0 !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Card Action Buttons */
.webchat .ac-pushButton {
  background-color: #FFF !important;
  border: 2px solid #E5384C !important;
  border-radius: 8px !important;
  color: #E5384C !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  margin: 4px !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
}

.webchat .ac-pushButton:hover {
  background-color: #E5384C !important;
  color: #FFF !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(229, 56, 76, 0.3) !important;
}

/* Send Box Button */
.webchat [data-testid="send-button"] {
  background-color: #E5384C !important;
  border-radius: 8px !important;
  transition: all 0.2s ease-in-out !important;
}

.webchat [data-testid="send-button"]:hover {
  background-color: #D21242 !important;
  transform: scale(1.05) !important;
}

/* Message bubbles enhancement */
.webchat [data-testid="message-bubble"] {
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  line-height: 1.5 !important;
}

/* Bot message bubbles */
.webchat [data-testid="message-bubble"][data-from="bot"] {
  background-color: #FFF !important;
  border: 1px solid #F3F0F0 !important;
  color: #2F2D2D !important;
}

/* User message bubbles with gradient */
.webchat [data-testid="message-bubble"][data-from="user"] {
  background: linear-gradient(90deg, #E5384C 0%, #EA714F 100%) !important;
  color: #FFF !important;
  border: none !important;
}

/* Typing Indicator */
.webchat [data-testid="typing-indicator"] {
  color: #716A6A !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Timestamp */
.webchat [data-testid="timestamp"] {
  color: #716A6A !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-size: 12px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin: 0 10px;
    border-radius: 8px;
  }

  .button {
    padding: 10px 16px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .card {
    margin: 0 5px;
  }

  .button {
    padding: 8px 12px;
    font-size: 12px;
  }

  .status-indicator {
    padding: 8px;
    font-size: 13px;
  }
}

/* Legacy styles removed - now using TypeScript theme system */
/* All component styling is handled through the centralized theme */

/* Quick Replies */
.webchat [data-testid="quick-reply"] {
  background-color: #FFF !important;
  border: 2px solid #009b65 !important;
  border-radius: 8px !important;
  color: #009b65 !important;
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  margin: 2px !important;
  transition: all 0.2s ease-in-out !important;
}

.webchat [data-testid="quick-reply"]:hover {
  background-color: #009b65 !important;
  color: #FFF !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(0, 155, 101, 0.3) !important;
}

/* Carousel Cards */
.webchat [data-testid="carousel"] {
  padding: 8px 0 !important;
}

.webchat [data-testid="carousel-item"] {
  background-color: #FFF !important;
  border: 1px solid #F3F0F0 !important;
  border-radius: 16px !important;
  box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
  margin: 0 8px !important;
  overflow: hidden !important;
}

/* Hero Cards */
.webchat .ac-container {
  background-color: #FFF !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
}

/* Card Titles */
.webchat .ac-textBlock {
  font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  color: #2F2D2D !important;
}

/* Card Images */
.webchat .ac-image {
  border-radius: 8px !important;
}