import React, { useState } from 'react'
import { validateToken, testTokenConnection } from '../utils/tokenValidator'
import { theme } from '../theme'

interface TokenTesterProps {
  token: string
}

/**
 * TokenTester - Component to test and validate Direct Line tokens
 * Helps diagnose connection issues
 */
export const TokenTester: React.FC<TokenTesterProps> = ({ token }) => {
  const [testResult, setTestResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleTestToken = async () => {
    setIsLoading(true)
    setTestResult(null)

    try {
      console.log('🧪 Testing token...', { tokenLength: token.length })
      const result = await validateToken(token)
      setTestResult(result)
      console.log('🧪 Token test result:', result)
    } catch (error) {
      console.error('❌ Token test error:', error)
      setTestResult({
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestConnection = async () => {
    setIsLoading(true)
    setTestResult(null)

    try {
      console.log('🔗 Testing connection...', { tokenLength: token.length })
      const result = await testTokenConnection(token)
      setTestResult({
        connectionTest: true,
        ...result
      })
      console.log('🔗 Connection test result:', result)
    } catch (error) {
      console.error('❌ Connection test error:', error)
      setTestResult({
        connectionTest: true,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const containerStyles: React.CSSProperties = {
    padding: theme.spacing[4],
    margin: theme.spacing[4],
    backgroundColor: theme.colors.neutral100,
    borderRadius: '8px',
    border: `1px solid ${theme.colors.neutral200}`,
    fontFamily: theme.fonts.families.primary,
  }

  const buttonStyles: React.CSSProperties = {
    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
    color: theme.colors.neutralWhite,
    border: 'none',
    padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
    borderRadius: '6px',
    fontSize: theme.fonts.sizes.sm,
    fontWeight: theme.fonts.weights.medium,
    cursor: 'pointer',
    marginRight: theme.spacing[2],
    marginBottom: theme.spacing[2],
    transition: 'all 0.2s ease-in-out',
  }

  const resultStyles: React.CSSProperties = {
    marginTop: theme.spacing[3],
    padding: theme.spacing[3],
    backgroundColor: theme.colors.neutralWhite,
    borderRadius: '6px',
    border: `1px solid ${theme.colors.neutral200}`,
    fontSize: theme.fonts.sizes.sm,
    fontFamily: 'monospace',
    whiteSpace: 'pre-wrap',
  }

  return (
    <div style={containerStyles}>
      <h3 style={{ 
        margin: `0 0 ${theme.spacing[3]} 0`,
        color: theme.colors.brandRed,
        fontSize: theme.fonts.sizes.lg,
        fontWeight: theme.fonts.weights.semibold
      }}>
        🧪 Direct Line Token Tester
      </h3>
      
      <p style={{ 
        margin: `0 0 ${theme.spacing[3]} 0`,
        color: theme.colors.neutral700,
        fontSize: theme.fonts.sizes.sm
      }}>
        Use these tools to diagnose connection issues with your Direct Line token.
      </p>

      <div style={{ marginBottom: theme.spacing[3] }}>
        <strong>Token Info:</strong>
        <ul style={{ margin: `${theme.spacing[1]} 0`, paddingLeft: theme.spacing[4] }}>
          <li>Length: {token.length} characters</li>
          <li>Starts with: {token.substring(0, 20)}...</li>
          <li>Ends with: ...{token.substring(token.length - 10)}</li>
        </ul>
      </div>

      <div>
        <button
          style={buttonStyles}
          onClick={handleTestToken}
          disabled={isLoading}
          onMouseEnter={(e) => {
            if (!isLoading) {
              e.currentTarget.style.transform = 'translateY(-1px)'
              e.currentTarget.style.boxShadow = theme.shadows.medium
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = 'none'
          }}
        >
          {isLoading ? '🔄 Testing...' : '🧪 Full Token Test'}
        </button>

        <button
          style={buttonStyles}
          onClick={handleTestConnection}
          disabled={isLoading}
          onMouseEnter={(e) => {
            if (!isLoading) {
              e.currentTarget.style.transform = 'translateY(-1px)'
              e.currentTarget.style.boxShadow = theme.shadows.medium
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = 'none'
          }}
        >
          {isLoading ? '🔄 Testing...' : '🔗 Connection Test'}
        </button>
      </div>

      {testResult && (
        <div style={resultStyles}>
          <strong style={{ color: testResult.isValid || testResult.success ? theme.colors.brandGreen : theme.colors.brandRed }}>
            {testResult.isValid || testResult.success ? '✅ Test Result' : '❌ Test Result'}
          </strong>
          <br /><br />
          {JSON.stringify(testResult, null, 2)}
        </div>
      )}
    </div>
  )
}

export default TokenTester
