import React from 'react'
import { theme } from '../../theme'

interface ChatLauncherButtonProps {
  /** Whether the chat is currently open */
  isOpen: boolean
  /** Click handler for the button */
  onClick: () => void
  /** Optional custom icon */
  icon?: string
  /** Optional custom size */
  size?: 'sm' | 'md' | 'lg'
  /** Optional custom position */
  position?: {
    bottom?: string
    right?: string
    left?: string
    top?: string
  }
}

/**
 * ChatLauncherButton - Floating chat launcher button component
 * Provides an accessible, themed button for opening/closing the chat widget
 */
export const ChatLauncherButton: React.FC<ChatLauncherButtonProps> = ({
  isOpen,
  onClick,
  icon = '💬',
  size = 'lg',
  position = { bottom: '20px', right: '20px' }
}) => {
  const sizeMap = {
    sm: { width: '48px', height: '48px', fontSize: '20px' },
    md: { width: '56px', height: '56px', fontSize: '24px' },
    lg: { width: '64px', height: '64px', fontSize: '28px' }
  }

  const buttonSize = sizeMap[size]

  const buttonStyles: React.CSSProperties = {
    position: 'fixed',
    bottom: position.bottom,
    right: position.right,
    left: position.left,
    top: position.top,
    width: buttonSize.width,
    height: buttonSize.height,
    borderRadius: '50%',
    border: 'none',
    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
    color: theme.colors.neutralWhite,
    fontSize: buttonSize.fontSize,
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: theme.shadows.large,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    zIndex: 1000,
    transform: isOpen ? 'rotate(45deg)' : 'rotate(0deg)',
    fontFamily: theme.fonts.families.primary,
    outline: 'none',
    WebkitTapHighlightColor: 'transparent',
  }

  const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.transform = isOpen ? 'rotate(45deg) scale(1.1)' : 'rotate(0deg) scale(1.1)'
    e.currentTarget.style.boxShadow = theme.shadows.large
  }

  const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.transform = isOpen ? 'rotate(45deg) scale(1)' : 'rotate(0deg) scale(1)'
    e.currentTarget.style.boxShadow = theme.shadows.large
  }

  const handleFocus = (e: React.FocusEvent<HTMLButtonElement>) => {
    e.currentTarget.style.outline = `2px solid ${theme.colors.brandRed}`
    e.currentTarget.style.outlineOffset = '2px'
  }

  const handleBlur = (e: React.FocusEvent<HTMLButtonElement>) => {
    e.currentTarget.style.outline = 'none'
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      onClick()
    }
  }

  return (
    <button
      style={buttonStyles}
      onClick={onClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      aria-label={isOpen ? 'Close chat' : 'Open chat'}
      aria-expanded={isOpen}
      role="button"
      tabIndex={0}
      title={isOpen ? 'Close chat' : 'Open chat'}
    >
      <span
        style={{
          display: 'inline-block',
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isOpen ? 'rotate(-45deg)' : 'rotate(0deg)',
        }}
      >
        {isOpen ? '×' : icon}
      </span>
    </button>
  )
}

export default ChatLauncherButton
