import React, { forwardRef } from 'react'
import { ButtonProps } from '@/types'
import { theme } from '@/theme'

/**
 * Reusable Button component with variants and sizes
 * Supports primary, secondary, success, and danger variants
 * Includes loading state and accessibility features
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    children, 
    variant = 'primary', 
    size = 'md', 
    disabled = false, 
    loading = false, 
    onClick, 
    type = 'button',
    className = '',
    ...props 
  }, ref) => {
    const baseStyles = theme.components.button.base
    const variantStyles = theme.components.button.variants[variant]
    const sizeStyles = theme.components.button.sizes[size]

    const buttonStyles: React.CSSProperties = {
      ...baseStyles,
      ...variantStyles,
      ...sizeStyles,
      opacity: disabled || loading ? theme.opacity.disabled : 1,
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      position: 'relative',
    }

    const handleClick = () => {
      if (!disabled && !loading && onClick) {
        onClick()
      }
    }

    return (
      <button
        ref={ref}
        type={type}
        style={buttonStyles}
        onClick={handleClick}
        disabled={disabled || loading}
        className={`button button--${variant} button--${size} ${className}`}
        aria-disabled={disabled || loading}
        aria-busy={loading}
        {...props}
      >
        {loading && (
          <span 
            style={{
              marginRight: theme.spacing[2],
              display: 'inline-block',
              width: '16px',
              height: '16px',
              border: '2px solid transparent',
              borderTop: '2px solid currentColor',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
            aria-hidden="true"
          />
        )}
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'

// CSS-in-JS styles for animations
const buttonAnimationStyles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .button {
    transition: all 0.2s ease-in-out;
  }
  
  .button:hover:not(:disabled) {
    transform: translateY(-1px);
  }
  
  .button:active:not(:disabled) {
    transform: translateY(0);
  }
  
  .button--primary:hover:not(:disabled) {
    background-color: ${theme.colors.brandDarkRed};
    box-shadow: ${theme.shadows.medium};
  }
  
  .button--secondary:hover:not(:disabled) {
    background-color: ${theme.colors.brandRed};
    color: ${theme.colors.neutralWhite};
    box-shadow: ${theme.shadows.medium};
  }
  
  .button--success:hover:not(:disabled) {
    background-color: ${theme.colors.successHover};
  }
`

// Inject styles into document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = buttonAnimationStyles
  document.head.appendChild(styleElement)
}

export default Button
