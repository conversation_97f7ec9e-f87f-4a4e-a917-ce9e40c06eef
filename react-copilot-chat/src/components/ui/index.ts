/**
 * UI Components barrel export
 * Centralized exports for all reusable UI components
 */

export { Button, default as ButtonComponent } from './Button'
export { 
  Card, 
  CardHeader, 
  CardBody, 
  CardFooter, 
  default as CardComponent 
} from './Card'
export {
  StatusIndicator,
  SuccessIndicator,
  ErrorIndicator,
  LoadingIndicator,
  WarningIndicator,
  InfoIndicator,
  default as StatusIndicatorComponent
} from './StatusIndicator'
export { ChatLauncherButton, default as ChatLauncherButtonComponent } from './ChatLauncherButton'
export { FloatingChatWindow, default as FloatingChatWindowComponent } from './FloatingChatWindow'

// Re-export types for convenience
export type { ButtonProps, CardProps, StatusIndicatorProps } from '@/types'
