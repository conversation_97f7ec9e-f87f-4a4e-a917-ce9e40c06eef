import React, { forwardRef } from 'react'
import { CardProps } from '@/types'
import { theme } from '@/theme'

/**
 * Reusable Card component for displaying content containers
 * Supports different variants (default, elevated, outlined) and padding options
 * Provides consistent styling across the application
 */
export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ 
    children, 
    variant = 'default', 
    padding = 'md',
    className = '',
    ...props 
  }, ref) => {
    const baseStyles = theme.components.card.base
    const variantStyles = variant !== 'default' ? theme.components.card.variants[variant] : {}
    const paddingValue = theme.components.card.padding[padding]

    const cardStyles: React.CSSProperties = {
      ...baseStyles,
      ...variantStyles,
      padding: paddingValue,
    }

    return (
      <div
        ref={ref}
        style={cardStyles}
        className={`card card--${variant} card--padding-${padding} ${className}`}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Card.displayName = 'Card'

/**
 * Card Header component for consistent card headers
 */
export const CardHeader = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ children, className = '', ...props }, ref) => {
    const headerStyles: React.CSSProperties = {
      padding: `${theme.spacing[4]} ${theme.spacing[4]} ${theme.spacing[3]} ${theme.spacing[4]}`,
      borderBottom: `${theme.borderWidths[1]} solid ${theme.colors.neutral200}`,
      backgroundColor: theme.colors.neutral50,
      fontWeight: theme.fonts.weights.semibold,
      fontSize: theme.fonts.sizes.lg,
      color: theme.colors.neutral700,
    }

    return (
      <div
        ref={ref}
        style={headerStyles}
        className={`card-header ${className}`}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CardHeader.displayName = 'CardHeader'

/**
 * Card Body component for main card content
 */
export const CardBody = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ children, className = '', ...props }, ref) => {
    const bodyStyles: React.CSSProperties = {
      padding: theme.spacing[4],
      flex: 1,
    }

    return (
      <div
        ref={ref}
        style={bodyStyles}
        className={`card-body ${className}`}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CardBody.displayName = 'CardBody'

/**
 * Card Footer component for card actions or additional info
 */
export const CardFooter = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ children, className = '', ...props }, ref) => {
    const footerStyles: React.CSSProperties = {
      padding: `${theme.spacing[3]} ${theme.spacing[4]} ${theme.spacing[4]} ${theme.spacing[4]}`,
      borderTop: `${theme.borderWidths[1]} solid ${theme.colors.neutral200}`,
      backgroundColor: theme.colors.neutral50,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: theme.spacing[3],
    }

    return (
      <div
        ref={ref}
        style={footerStyles}
        className={`card-footer ${className}`}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CardFooter.displayName = 'CardFooter'

export default Card
