import React, { useEffect, useRef } from 'react'
import { theme } from '../../theme'

interface FloatingChatWindowProps {
  /** Whether the chat window is open */
  isOpen: boolean
  /** Child components to render inside the window */
  children: React.ReactNode
  /** Custom width for the chat window */
  width?: string
  /** Custom height for the chat window */
  height?: string
  /** Position configuration */
  position?: {
    bottom?: string
    right?: string
    left?: string
    top?: string
  }
  /** Animation duration in milliseconds */
  animationDuration?: number
  /** Whether to show on mobile devices */
  showOnMobile?: boolean
}

/**
 * FloatingChatWindow - Container for the floating chat interface
 * Provides positioning, animations, and responsive behavior
 */
export const FloatingChatWindow: React.FC<FloatingChatWindowProps> = ({
  isOpen,
  children,
  width = '400px',
  height = '600px',
  position = { bottom: '100px', right: '20px' },
  animationDuration = 300,
  showOnMobile = true
}) => {
  const windowRef = useRef<HTMLDivElement>(null)

  // Handle escape key to close chat
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        // Let parent handle the close action
        event.preventDefault()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scroll when chat is open
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  // Focus management for accessibility
  useEffect(() => {
    if (isOpen && windowRef.current) {
      // Focus the chat window when it opens
      const focusableElement = windowRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement
      
      if (focusableElement) {
        setTimeout(() => focusableElement.focus(), 100)
      }
    }
  }, [isOpen])

  const containerStyles: React.CSSProperties = {
    position: 'fixed',
    bottom: position.bottom,
    right: position.right,
    left: position.left,
    top: position.top,
    width,
    height,
    zIndex: 999,
    transform: isOpen ? 'translateY(0) scale(1)' : 'translateY(20px) scale(0.95)',
    opacity: isOpen ? 1 : 0,
    visibility: isOpen ? 'visible' : 'hidden',
    transition: `all ${animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
    transformOrigin: 'bottom right',
    pointerEvents: isOpen ? 'auto' : 'none',
  }

  const overlayStyles: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    zIndex: 998,
    opacity: isOpen ? 1 : 0,
    visibility: isOpen ? 'visible' : 'hidden',
    transition: `all ${animationDuration}ms ease-in-out`,
    pointerEvents: isOpen ? 'auto' : 'none',
    backdropFilter: 'blur(2px)',
  }

  const windowStyles: React.CSSProperties = {
    width: '100%',
    height: '100%',
    backgroundColor: theme.colors.neutralWhite,
    borderRadius: '16px',
    boxShadow: theme.shadows.large,
    border: `1px solid ${theme.colors.neutral200}`,
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  }

  // Apply mobile styles if on mobile device
  const isMobile = window.innerWidth <= 768
  const finalContainerStyles = isMobile && showOnMobile 
    ? {
        ...containerStyles,
        width: 'calc(100vw - 20px)',
        height: 'calc(100vh - 100px)',
        bottom: '10px',
        right: '10px',
        left: '10px',
        top: 'auto',
        transformOrigin: 'bottom center',
      }
    : containerStyles

  if (!showOnMobile && isMobile) {
    return null
  }

  return (
    <>
      {/* Backdrop overlay for mobile */}
      {isMobile && (
        <div style={overlayStyles} />
      )}
      
      {/* Chat window container */}
      <div
        ref={windowRef}
        style={finalContainerStyles}
        role="dialog"
        aria-modal="true"
        aria-label="Chat window"
      >
        <div style={windowStyles}>
          {children}
        </div>
      </div>
    </>
  )
}

export default FloatingChatWindow
