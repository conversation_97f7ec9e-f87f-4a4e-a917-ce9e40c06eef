/**
 * Mode Dropdown Component
 * Provides a dropdown menu for switching between different application modes
 */

import React, { useState, useRef, useEffect } from 'react'
import { theme } from '../../theme'

export type ViewMode = 'demo' | 'fullpage' | 'debug' | 'richcards'

interface ModeOption {
  value: ViewMode
  label: string
  icon: string
  description: string
}

interface ModeDropdownProps {
  currentMode: ViewMode
  onModeChange: (mode: ViewMode) => void
}

const modeOptions: ModeOption[] = [
  {
    value: 'demo',
    label: 'Demo Mode',
    icon: '💬',
    description: 'Floating chat launcher with demo content'
  },
  {
    value: 'fullpage',
    label: 'Full Page Mode',
    icon: '📄',
    description: 'Traditional full-page WebChat interface'
  },
  {
    value: 'debug',
    label: 'Debug Mode',
    icon: '🧪',
    description: 'Token testing and troubleshooting tools'
  },
  {
    value: 'richcards',
    label: 'Rich Cards Demo',
    icon: '🎨',
    description: 'Live examples of rich card components'
  }
]

export const ModeDropdown: React.FC<ModeDropdownProps> = ({ currentMode, onModeChange }) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const currentOption = modeOptions.find(option => option.value === currentMode) || modeOptions[0]

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleOptionClick = (mode: ViewMode) => {
    onModeChange(mode)
    setIsOpen(false)
  }

  const dropdownStyles: React.CSSProperties = {
    position: 'fixed',
    top: theme.spacing[4],
    left: theme.spacing[4],
    zIndex: 1000,
    fontFamily: theme.fonts.families.primary,
  }

  const triggerStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing[2],
    padding: `${theme.spacing[2]} ${theme.spacing[3]}`,
    backgroundColor: theme.colors.neutralWhite,
    border: `1px solid ${theme.colors.neutral300}`,
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: theme.fonts.sizes.sm,
    fontWeight: theme.fonts.weights.medium,
    color: theme.colors.neutral800,
    boxShadow: theme.shadows.small,
    transition: 'all 0.2s ease-in-out',
    minWidth: '180px',
    justifyContent: 'space-between',
  }

  const menuStyles: React.CSSProperties = {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    marginTop: theme.spacing[1],
    backgroundColor: theme.colors.neutralWhite,
    border: `1px solid ${theme.colors.neutral300}`,
    borderRadius: '8px',
    boxShadow: theme.shadows.large,
    overflow: 'hidden',
    zIndex: 1001,
  }

  const optionStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'flex-start',
    gap: theme.spacing[2],
    padding: theme.spacing[3],
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
    borderBottom: `1px solid ${theme.colors.neutral100}`,
  }

  const optionContentStyles: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing[1],
  }

  const optionLabelStyles: React.CSSProperties = {
    fontSize: theme.fonts.sizes.sm,
    fontWeight: theme.fonts.weights.medium,
    color: theme.colors.neutral800,
  }

  const optionDescriptionStyles: React.CSSProperties = {
    fontSize: theme.fonts.sizes.xs,
    color: theme.colors.neutral600,
    lineHeight: theme.fonts.lineHeights.tight,
  }

  const chevronStyles: React.CSSProperties = {
    fontSize: '12px',
    color: theme.colors.neutral600,
    transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
    transition: 'transform 0.2s ease-in-out',
  }

  return (
    <div ref={dropdownRef} style={dropdownStyles}>
      <button
        style={triggerStyles}
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={(e) => {
          e.currentTarget.style.borderColor = theme.colors.brandRed
          e.currentTarget.style.boxShadow = theme.shadows.medium
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.borderColor = theme.colors.neutral300
          e.currentTarget.style.boxShadow = theme.shadows.small
        }}
        aria-label="Select application mode"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
          <span style={{ fontSize: '16px' }}>{currentOption.icon}</span>
          <span>{currentOption.label}</span>
        </div>
        <span style={chevronStyles}>▼</span>
      </button>

      {isOpen && (
        <div style={menuStyles} role="listbox">
          {modeOptions.map((option) => {
            const isSelected = option.value === currentMode
            const optionStylesWithHover = {
              ...optionStyles,
              backgroundColor: isSelected ? theme.colors.brandLightRed : 'transparent',
            }

            return (
              <div
                key={option.value}
                style={optionStylesWithHover}
                onClick={() => handleOptionClick(option.value)}
                onMouseEnter={(e) => {
                  if (!isSelected) {
                    e.currentTarget.style.backgroundColor = theme.colors.neutral50
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSelected) {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }
                }}
                role="option"
                aria-selected={isSelected}
              >
                <span style={{ fontSize: '16px', marginTop: '2px' }}>{option.icon}</span>
                <div style={optionContentStyles}>
                  <div style={optionLabelStyles}>{option.label}</div>
                  <div style={optionDescriptionStyles}>{option.description}</div>
                </div>
                {isSelected && (
                  <span style={{ 
                    color: theme.colors.brandRed, 
                    fontSize: '14px',
                    marginTop: '2px'
                  }}>
                    ✓
                  </span>
                )}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default ModeDropdown
