import React, { forwardRef } from 'react'
import { StatusIndicatorProps } from '@/types'
import { theme } from '@/theme'

/**
 * StatusIndicator component for displaying connection status and other states
 * Supports success, error, warning, info, and loading states
 * Includes proper accessibility attributes and visual indicators
 */
export const StatusIndicator = forwardRef<HTMLDivElement, StatusIndicatorProps>(
  ({ 
    status, 
    title, 
    description, 
    icon,
    className = '',
    children,
    ...props 
  }, ref) => {
    const baseStyles = theme.components.statusIndicator.base
    const variantStyles = status !== 'loading' 
      ? theme.components.statusIndicator.variants[status]
      : theme.components.statusIndicator.variants.info

    const containerStyles: React.CSSProperties = {
      ...baseStyles,
      ...variantStyles,
    }

    // Default icons for each status
    const getDefaultIcon = (status: StatusIndicatorProps['status']): string => {
      switch (status) {
        case 'success':
          return '✅'
        case 'error':
          return '❌'
        case 'warning':
          return '⚠️'
        case 'info':
          return 'ℹ️'
        case 'loading':
          return '🔄'
        default:
          return '•'
      }
    }

    const displayIcon = icon || getDefaultIcon(status)

    return (
      <div
        ref={ref}
        style={containerStyles}
        className={`status-indicator status-indicator--${status} ${className}`}
        role="status"
        aria-live={status === 'error' ? 'assertive' : 'polite'}
        aria-label={`${status}: ${title}`}
        {...props}
      >
        <span 
          className="status-indicator__icon"
          style={{
            fontSize: theme.fonts.sizes.xl,
            lineHeight: 1,
            animation: status === 'loading' ? 'spin 2s linear infinite' : 'none',
          }}
          aria-hidden="true"
        >
          {displayIcon}
        </span>
        
        <div className="status-indicator__content">
          <div 
            className="status-indicator__title"
            style={{
              fontWeight: theme.fonts.weights.semibold,
              fontSize: theme.fonts.sizes.base,
              margin: 0,
              lineHeight: theme.fonts.lineHeights.normal,
            }}
          >
            {title}
          </div>
          
          {description && (
            <div 
              className="status-indicator__description"
              style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
                margin: `${theme.spacing[1]} 0 0 0`,
                lineHeight: theme.fonts.lineHeights.normal,
              }}
            >
              {description}
            </div>
          )}
          
          {children && (
            <div 
              className="status-indicator__extra"
              style={{
                marginTop: theme.spacing[2],
              }}
            >
              {children}
            </div>
          )}
        </div>
      </div>
    )
  }
)

StatusIndicator.displayName = 'StatusIndicator'

/**
 * Specialized components for common status types
 */
export const SuccessIndicator = forwardRef<HTMLDivElement, Omit<StatusIndicatorProps, 'status'>>(
  (props, ref) => <StatusIndicator ref={ref} status="success" {...props} />
)

export const ErrorIndicator = forwardRef<HTMLDivElement, Omit<StatusIndicatorProps, 'status'>>(
  (props, ref) => <StatusIndicator ref={ref} status="error" {...props} />
)

export const LoadingIndicator = forwardRef<HTMLDivElement, Omit<StatusIndicatorProps, 'status'>>(
  (props, ref) => <StatusIndicator ref={ref} status="loading" {...props} />
)

export const WarningIndicator = forwardRef<HTMLDivElement, Omit<StatusIndicatorProps, 'status'>>(
  (props, ref) => <StatusIndicator ref={ref} status="warning" {...props} />
)

export const InfoIndicator = forwardRef<HTMLDivElement, Omit<StatusIndicatorProps, 'status'>>(
  (props, ref) => <StatusIndicator ref={ref} status="info" {...props} />
)

// Add animation styles
const statusIndicatorStyles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .status-indicator {
    transition: all 0.2s ease-in-out;
  }
  
  .status-indicator:hover {
    transform: translateY(-1px);
  }
`

// Inject styles into document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = statusIndicatorStyles
  document.head.appendChild(styleElement)
}

export default StatusIndicator
