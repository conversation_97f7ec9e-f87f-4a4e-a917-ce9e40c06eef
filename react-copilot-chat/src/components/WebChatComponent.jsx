import { useState, useRef, useEffect } from 'react'
import { createDirectLine, createStyleSet, renderWebChat } from 'botframework-webchat'
import './WebChatComponent.css'

const WebChatComponent = () => {
  // Get token from environment variables, fallback to manual input
  const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
  const botName = import.meta.env.VITE_BOT_NAME || 'Copilot Assistant'
  const welcomeMessage = import.meta.env.VITE_WELCOME_MESSAGE || 'Hello! 👋 Welcome to our Copilot Studio integration. How can I help you today?'

  const [token, setToken] = useState(envToken && envToken !== 'your_direct_line_token_here' ? envToken : '')
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState('')
  const [useEnvToken] = useState(envToken && envToken !== 'your_direct_line_token_here')
  const webchatRef = useRef(null)
  const directLineRef = useRef(null)

  // Auto-connect if environment token is available
  useEffect(() => {
    if (useEnvToken && token && !isConnected && !isConnecting) {
      console.log('Auto-connecting with environment token...')
      connectToBot()
    }
  }, [useEnvToken, token, isConnected, isConnecting])

  // Your company's design tokens
  const brandTokens = {
    // Brand colors
    brandRed: "#E5384C",
    brandOrange: "#EA714F",
    brandDarkRed: "#D21242",
    brandLightRed: "#F9C7CC",

    // Neutrals
    neutralWhite: "#FFF",
    neutral50: "#FCFAFA",
    neutral100: "#F8F6F6",
    neutral300: "#F3F0F0",
    neutral400: "#DFDCDC",
    neutral800: "#716A6A",
    neutral900: "#2F2D2D",

    // Secondary colors
    accentGreen600: "#009b65",
    accentGreen700: "#007250",
    purple500: "#8D8CC6",
    blue500: "#72BDCE",

    // Typography
    fontFamily: "Etelka, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",

    // Spacing (4px grid)
    spacing: {
      xs: 4,
      s: 8,
      m: 16,
      l: 24,
      xl: 32
    },

    // Border radius
    borderRadius: {
      s: 8,
      m: 16,
      l: 24
    }
  }

  // Custom style set for Web Chat with your branding
  const styleSet = createStyleSet({
    // Primary brand colors
    primaryColor: brandTokens.brandRed,
    accent: brandTokens.brandOrange,

    // Background colors
    backgroundColor: brandTokens.neutral50,

    // Chat bubble styling with your brand
    bubbleBackground: brandTokens.neutralWhite,
    bubbleFromUserBackground: `linear-gradient(90deg, ${brandTokens.brandRed} 0%, ${brandTokens.brandOrange} 100%)`,
    bubbleFromUserTextColor: brandTokens.neutralWhite,
    bubbleTextColor: brandTokens.neutral900,
    bubbleBorder: `1px solid ${brandTokens.neutral300}`,

    // Border radius using your design tokens
    bubbleBorderRadius: brandTokens.borderRadius.s,
    bubbleFromUserBorderRadius: brandTokens.borderRadius.s,

    // Avatar styling
    avatarSize: 40,
    botAvatarBackgroundColor: brandTokens.brandRed,
    userAvatarBackgroundColor: brandTokens.accentGreen600,

    // Input box styling
    sendBoxBackground: brandTokens.neutralWhite,
    sendBoxBorderTop: `2px solid ${brandTokens.neutral300}`,
    sendBoxTextColor: brandTokens.neutral900,
    sendBoxPlaceholderColor: brandTokens.neutral800,
    sendBoxButtonColor: brandTokens.brandRed,
    sendBoxButtonColorOnHover: brandTokens.brandDarkRed,
    sendBoxButtonColorOnFocus: brandTokens.brandDarkRed,

    // Suggested actions (buttons) with your brand styling
    suggestedActionBackground: brandTokens.neutralWhite,
    suggestedActionBorder: `2px solid ${brandTokens.brandRed}`,
    suggestedActionBorderOnHover: `2px solid ${brandTokens.brandDarkRed}`,
    suggestedActionTextColor: brandTokens.brandRed,
    suggestedActionTextColorOnHover: brandTokens.brandDarkRed,
    suggestedActionBorderRadius: brandTokens.borderRadius.s,

    // Cards and rich content
    cardEmphasisBackgroundColor: brandTokens.neutral100,

    // Scrollbar and other elements
    scrollToEndButtonBackgroundColor: brandTokens.brandRed,
    timestampColor: brandTokens.neutral800,

    // Typography using your font family
    fontSizeSmall: '12px',
    primaryFont: brandTokens.fontFamily,

    // Additional customizations
    hideUploadButton: false,
    botAvatarInitials: '🤖',
    userAvatarInitials: 'You'
  })

  // Advanced styling customizations using your design tokens
  styleSet.textContent = {
    ...styleSet.textContent,
    fontFamily: brandTokens.fontFamily,
    fontSize: '16px', // Using your BodyM size
    lineHeight: 1.5,
    color: brandTokens.neutral900
  }

  // Custom button styling for suggested actions
  styleSet.suggestedAction = {
    ...styleSet.suggestedAction,
    backgroundColor: brandTokens.neutralWhite,
    border: `2px solid ${brandTokens.brandRed}`,
    borderRadius: brandTokens.borderRadius.s,
    color: brandTokens.brandRed,
    fontFamily: brandTokens.fontFamily,
    fontWeight: '500',
    padding: `${brandTokens.spacing.s}px ${brandTokens.spacing.m}px`,
    transition: 'all 0.2s ease-in-out',
    cursor: 'pointer'
  }

  // Enhanced hover effects via CSS (will be added to CSS file)

  // Card styling for rich content
  styleSet.adaptiveCardRenderer = {
    ...styleSet.adaptiveCardRenderer,
    backgroundColor: brandTokens.neutralWhite,
    border: `1px solid ${brandTokens.neutral300}`,
    borderRadius: brandTokens.borderRadius.m,
    boxShadow: '0 2px 8px rgba(47, 45, 45, 0.1)',
    padding: brandTokens.spacing.m
  }

  // Custom activity renderer for enhanced styling
  const customActivityMiddleware = () => next => card => {
    const { activity } = card

    // Add custom styling for different activity types
    if (activity.type === 'message' && activity.suggestedActions) {
      // Style suggested actions with your brand
      const styledCard = {
        ...card,
        activity: {
          ...activity,
          suggestedActions: {
            ...activity.suggestedActions,
            actions: activity.suggestedActions.actions.map(action => ({
              ...action,
              style: {
                backgroundColor: brandTokens.neutralWhite,
                border: `2px solid ${brandTokens.brandRed}`,
                borderRadius: `${brandTokens.borderRadius.s}px`,
                color: brandTokens.brandRed,
                fontFamily: brandTokens.fontFamily,
                fontWeight: '500',
                padding: `${brandTokens.spacing.s}px ${brandTokens.spacing.m}px`,
                margin: `${brandTokens.spacing.xs}px`,
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }
            }))
          }
        }
      }
      return next(styledCard)
    }

    return next(card)
  }

  const connectToBot = async () => {
    if (!token.trim()) {
      setError('Please enter a Direct Line token')
      return
    }

    setIsConnecting(true)
    setError('')

    try {
      // Create Direct Line connection
      const directLine = createDirectLine({
        token: token.trim()
      })

      directLineRef.current = directLine

      // Clear the webchat container
      if (webchatRef.current) {
        webchatRef.current.innerHTML = ''
      }

      // Render Web Chat
      console.log('Rendering WebChat to container:', webchatRef.current)
      console.log('Container dimensions:', webchatRef.current?.offsetWidth, 'x', webchatRef.current?.offsetHeight)

      // Use the most minimal WebChat configuration
      renderWebChat(
        {
          directLine: directLine
        },
        webchatRef.current
      )

      // Debug: Check if WebChat rendered properly
      setTimeout(() => {
        console.log('WebChat container after render:', webchatRef.current?.innerHTML?.length > 0 ? 'Has content' : 'Empty')
        console.log('WebChat children count:', webchatRef.current?.children?.length)
      }, 1000)

      // Handle connection status with better error handling
      directLine.connectionStatus$.subscribe({
        next: (connectionStatus) => {
          console.log('Connection status:', connectionStatus, 'Status meanings: 0=Uninitialized, 1=Connecting, 2=Online, 3=ExpiredToken, 4=FailedToConnect, 5=Ended')

          if (connectionStatus === 2) { // Online
            setIsConnected(true)
            setIsConnecting(false)
            console.log('✅ WebChat connected successfully!')

            // Just send a simple join event, let the bot handle welcome messages
            setTimeout(() => {
              directLine.postActivity({
                type: 'event',
                name: 'webchat/join',
                from: { id: 'user' }
              }).subscribe({
                next: () => console.log('Join event sent successfully'),
                error: (error) => console.error('Error sending join event:', error)
              })
            }, 1000)

          } else if (connectionStatus === 3) { // ExpiredToken
            console.log('❌ Token expired')
            setError('Token expired. Please check your Direct Line token.')
            setIsConnecting(false)
            setIsConnected(false)
          } else if (connectionStatus === 4) { // FailedToConnect
            console.log('❌ Failed to connect')
            setError('Failed to connect. Please check your token and try again.')
            setIsConnecting(false)
            setIsConnected(false)
          } else if (connectionStatus === 5) { // Ended
            console.log('❌ Connection ended')
            setError('Connection ended unexpectedly.')
            setIsConnecting(false)
            setIsConnected(false)
          }
        },
        error: (error) => {
          console.error('❌ Connection subscription error:', error)
          setError('Connection error: ' + error.message)
          setIsConnecting(false)
          setIsConnected(false)
        }
      })

    } catch (err) {
      console.error('Error connecting to bot:', err)
      setError('Error connecting to bot: ' + err.message)
      setIsConnecting(false)
      setIsConnected(false)
    }
  }

  const disconnect = () => {
    if (directLineRef.current) {
      // Note: DirectLine doesn't have a direct disconnect method
      // We'll just reset the component state
      directLineRef.current = null
    }

    if (webchatRef.current) {
      webchatRef.current.innerHTML = `
        <div class="status">
          <p>👆 Enter your Direct Line token above to start chatting</p>
          <small>Get your token from Azure Bot Service → Channels → Direct Line</small>
        </div>
      `
    }

    setIsConnected(false)
    setIsConnecting(false)
    setError('')
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !isConnecting) {
      connectToBot()
    }
  }

  useEffect(() => {
    // Initialize the webchat container with placeholder content
    if (webchatRef.current && !isConnected) {
      if (useEnvToken) {
        webchatRef.current.innerHTML = `
          <div class="status">
            <p>🔄 Auto-connecting using environment token...</p>
            <small>Token loaded from .env file</small>
          </div>
        `
      } else {
        webchatRef.current.innerHTML = `
          <div class="status">
            <p>👆 Enter your Direct Line token above to start chatting</p>
            <small>Get your token from Azure Bot Service → Channels → Direct Line</small>
          </div>
        `
      }
    }
  }, [isConnected, useEnvToken])

  // Auto-connect if environment token is available
  useEffect(() => {
    if (useEnvToken && !isConnected && !isConnecting) {
      setTimeout(() => {
        connectToBot()
      }, 1000) // Small delay to show the auto-connecting message
    }
  }, [useEnvToken]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="webchat-container">
      <div className="config-panel">
        {useEnvToken ? (
          // Show environment token status
          <div className="env-token-status">
            <div className="token-info">
              <span className="token-indicator">🔐</span>
              <div className="token-details">
                <p><strong>Using Environment Token</strong></p>
                <small>Token loaded from .env file</small>
              </div>
            </div>
            <div className="button-group">
              {!isConnected ? (
                <button
                  onClick={connectToBot}
                  disabled={isConnecting}
                  className={`connect-btn ${isConnecting ? 'connecting' : ''}`}
                >
                  {isConnecting ? '🔄 Connecting...' : 'Connect Chat'}
                </button>
              ) : (
                <button
                  onClick={disconnect}
                  className="disconnect-btn"
                >
                  ✅ Connected - Disconnect
                </button>
              )}
            </div>
          </div>
        ) : (
          // Show manual token input
          <div className="token-input-group">
            <input
              type="password"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Enter your Direct Line token here..."
              disabled={isConnecting}
              className="token-input"
            />
            <div className="button-group">
              {!isConnected ? (
                <button
                  onClick={connectToBot}
                  disabled={isConnecting || !token.trim()}
                  className={`connect-btn ${isConnecting ? 'connecting' : ''}`}
                >
                  {isConnecting ? '🔄 Connecting...' : 'Connect Chat'}
                </button>
              ) : (
                <button
                  onClick={disconnect}
                  className="disconnect-btn"
                >
                  ✅ Connected - Disconnect
                </button>
              )}
            </div>
          </div>
        )}

        {error && (
          <div className="error-message">
            <strong>❌ Error:</strong> {error}
          </div>
        )}
      </div>

      <div className="webchat-wrapper">
        <div ref={webchatRef} className="webchat" />
      </div>
    </div>
  )
}

export default WebChatComponent