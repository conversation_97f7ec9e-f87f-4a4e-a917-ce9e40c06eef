import React, { useState, useEffect } from 'react'
import React<PERSON>eb<PERSON><PERSON>, { createDirectLine, createStyleSet } from 'botframework-webchat'
import './WebChatComponent.css'

const WebChatComponent = () => {
  // Get token from environment variables only
  const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
  const botName = import.meta.env.VITE_BOT_NAME || 'Copilot Assistant'

  const [directLine, setDirectLine] = useState(null)
  const [error, setError] = useState('')
  const [isInitializing, setIsInitializing] = useState(true)

  // Initialize DirectLine connection on component mount
  useEffect(() => {
    const initializeWebChat = async () => {
      console.log('🚀 Initializing WebChat...')

      // Check if token is available
      if (!envToken || envToken === 'your_direct_line_token_here') {
        setError('Direct Line token not configured. Please set VITE_DIRECT_LINE_TOKEN in your .env file.')
        setIsInitializing(false)
        return
      }

      try {
        console.log('🔗 Creating DirectLine connection...')

        // Create DirectLine connection
        const dl = createDirectLine({
          token: envToken.trim()
        })

        // Set up connection status monitoring
        dl.connectionStatus$.subscribe({
          next: (connectionStatus) => {
            console.log('Connection status:', connectionStatus)
            if (connectionStatus === 2) { // Online
              console.log('✅ WebChat connected successfully!')
            } else if (connectionStatus === 4) { // FailedToConnect
              setError('Failed to connect to bot. Please check your Direct Line token.')
            }
          },
          error: (error) => {
            console.error('❌ Connection error:', error)
            setError('Connection error: ' + error.message)
          }
        })

        setDirectLine(dl)
        setIsInitializing(false)

      } catch (error) {
        console.error('❌ Error initializing WebChat:', error)
        setError('Error initializing WebChat: ' + error.message)
        setIsInitializing(false)
      }
    }

    initializeWebChat()
  }, [envToken])

  // Create style set for custom branding
  const styleSet = createStyleSet({
    // Basic styling to ensure visibility
    rootHeight: '100%',
    rootWidth: '100%',

    // Brand colors
    primaryColor: '#E5384C',
    accent: '#EA714F',

    // Typography
    fontFamily: "'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",

    // Message bubbles
    bubbleBackground: '#FFF',
    bubbleBorderColor: '#F3F0F0',
    bubbleBorderRadius: 16,
    bubbleFromUserBackground: 'linear-gradient(90deg, #E5384C 0%, #EA714F 100%)',
    bubbleFromUserTextColor: '#FFF',

    // Send box
    sendBoxBackground: '#FFF',
    sendBoxBorderColor: '#F3F0F0',
    sendBoxButtonColor: '#E5384C',
    sendBoxButtonColorOnHover: '#D21242',
    sendBoxTextColor: '#2F2D2D'
  })

  // Render the component
  if (isInitializing) {
    return (
      <div className="webchat-container">
        <div className="config-panel">
          <div className="env-token-status">
            <div className="token-info">
              <span className="token-indicator">🔄</span>
              <div className="token-details">
                <p>Initializing WebChat...</p>
                <small>Connecting to {botName}</small>
              </div>
            </div>
          </div>
        </div>
        <div className="webchat-wrapper">
          <div className="webchat">
            <div className="status">
              <p>Initializing chat...</p>
              <small>Please wait while we connect to the bot</small>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="webchat-container">
        <div className="config-panel">
          <div className="env-token-status">
            <div className="token-info">
              <span className="token-indicator">❌</span>
              <div className="token-details">
                <p>Configuration Error</p>
                <small>WebChat cannot be initialized</small>
              </div>
            </div>
          </div>
        </div>
        <div className="webchat-wrapper">
          <div className="webchat">
            <div className="status">
              <p>Configuration Error</p>
              <small>{error}</small>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!directLine) {
    return (
      <div className="webchat-container">
        <div className="config-panel">
          <div className="env-token-status">
            <div className="token-info">
              <span className="token-indicator">⚠️</span>
              <div className="token-details">
                <p>Connection Issue</p>
                <small>DirectLine not available</small>
              </div>
            </div>
          </div>
        </div>
        <div className="webchat-wrapper">
          <div className="webchat">
            <div className="status">
              <p>Connection Issue</p>
              <small>DirectLine connection not established</small>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Main WebChat render
  return (
    <div className="webchat-container">
      <div className="config-panel">
        <div className="env-token-status">
          <div className="token-info">
            <span className="token-indicator">✅</span>
            <div className="token-details">
              <p>Connected to {botName}</p>
              <small>Using environment token</small>
            </div>
          </div>
        </div>
      </div>
      <div className="webchat-wrapper">
        <div className="webchat">
          <ReactWebChat
            directLine={directLine}
            styleSet={styleSet}
            userID={'user-' + Math.random().toString(36).substring(2, 11)}
            locale="en-US"
          />
        </div>
      </div>
    </div>
  )
}

export default WebChatComponent