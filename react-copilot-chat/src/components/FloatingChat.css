/* Floating Chat Animations and Responsive Styles */

/* Keyframe animations for smooth interactions */
@keyframes float-in {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

@keyframes button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(229, 56, 76, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(229, 56, 76, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(229, 56, 76, 0);
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Chat launcher button styles */
.chat-launcher-button {
  position: fixed;
  z-index: 1000;
  border: none;
  cursor: pointer;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.chat-launcher-button:hover {
  animation: button-pulse 2s infinite;
}

.chat-launcher-button:focus {
  outline: 2px solid #E5384C;
  outline-offset: 2px;
}

.chat-launcher-button:active {
  transform: scale(0.95);
}

/* Floating chat window styles */
.floating-chat-window {
  position: fixed;
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom right;
}

.floating-chat-window.open {
  animation: float-in 0.3s ease-out forwards;
}

.floating-chat-window.closed {
  animation: float-out 0.3s ease-in forwards;
}

/* Chat window content */
.chat-window-content {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
}

/* Mobile overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  z-index: 998;
  animation: fade-in 0.3s ease-out;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .floating-chat-window {
    width: calc(100vw - 20px) !important;
    height: calc(100vh - 100px) !important;
    bottom: 10px !important;
    right: 10px !important;
    left: 10px !important;
    transform-origin: bottom center;
  }

  .floating-chat-window.open {
    animation: slide-up 0.3s ease-out forwards;
  }

  .chat-launcher-button {
    width: 56px !important;
    height: 56px !important;
    font-size: 24px !important;
  }

  .chat-window-content {
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .floating-chat-window {
    width: calc(100vw - 10px) !important;
    height: calc(100vh - 80px) !important;
    bottom: 5px !important;
    right: 5px !important;
    left: 5px !important;
  }

  .chat-launcher-button {
    width: 48px !important;
    height: 48px !important;
    font-size: 20px !important;
    bottom: 15px !important;
    right: 15px !important;
  }

  .chat-window-content {
    border-radius: 8px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-launcher-button {
    border: 2px solid currentColor;
  }

  .chat-window-content {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chat-launcher-button,
  .floating-chat-window,
  .chat-window-content {
    transition: none;
    animation: none;
  }

  .chat-launcher-button:hover {
    animation: none;
  }

  .floating-chat-window.open,
  .floating-chat-window.closed {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .chat-window-content {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .mobile-overlay {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* Focus management for accessibility */
.chat-window-content:focus-within {
  outline: 2px solid #E5384C;
  outline-offset: 2px;
}

/* Smooth scrolling for chat content */
.chat-content {
  scroll-behavior: smooth;
}

/* Loading states */
.chat-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

.chat-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #E5384C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.chat-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
  color: #dc2626;
}

/* Success states */
.chat-success {
  color: #059669;
}

/* Interactive elements */
.chat-interactive {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.chat-interactive:hover {
  transform: translateY(-1px);
}

.chat-interactive:active {
  transform: translateY(0);
}

/* Notification badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #dc2626;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: button-pulse 2s infinite;
}
