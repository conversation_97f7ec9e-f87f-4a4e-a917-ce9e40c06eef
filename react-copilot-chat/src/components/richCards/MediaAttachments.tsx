/**
 * Media Attachment Components for Rich Bot Responses
 * Handles images, videos, audio, and file downloads
 */

import React, { useState, useRef } from 'react'
import { theme } from '../../theme'

// Image Attachment Component
interface ImageAttachmentProps {
  src: string
  alt?: string
  title?: string
  caption?: string
  maxWidth?: number
  maxHeight?: number
}

export const ImageAttachment: React.FC<ImageAttachmentProps> = ({
  src,
  alt = 'Image attachment',
  title,
  caption,
  maxWidth = 400,
  maxHeight = 300
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    overflow: 'hidden',
    fontFamily: theme.fonts.families.primary,
  }

  const imageStyles: React.CSSProperties = {
    width: '100%',
    maxWidth: `${maxWidth}px`,
    maxHeight: `${maxHeight}px`,
    height: 'auto',
    objectFit: 'cover',
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
  }

  const fullscreenStyles: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10000,
    cursor: 'pointer',
  }

  const handleImageLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleImageError = () => {
    setIsLoading(false)
    setHasError(true)
    console.warn('Failed to load image:', src)
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  if (hasError) {
    return (
      <div style={containerStyles}>
        <div style={{
          padding: theme.spacing[4],
          textAlign: 'center',
          color: theme.colors.error,
        }}>
          <div style={{ fontSize: '48px', marginBottom: theme.spacing[2] }}>🖼️</div>
          <p>Failed to load image</p>
          <small style={{ color: theme.colors.neutral600 }}>{src}</small>
        </div>
      </div>
    )
  }

  return (
    <>
      <div style={containerStyles}>
        {title && (
          <div style={{
            padding: `${theme.spacing[3]} ${theme.spacing[3]} 0`,
            fontWeight: theme.fonts.weights.bold,
            color: theme.colors.brandRed,
          }}>
            {title}
          </div>
        )}
        
        <div style={{ position: 'relative' }}>
          {isLoading && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: theme.colors.neutral100,
              minHeight: '200px',
              borderRadius: '6px',
            }}>
              <div style={{
                color: theme.colors.neutral600,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: theme.spacing[2]
              }}>
                <div style={{
                  width: '24px',
                  height: '24px',
                  border: `2px solid ${theme.colors.neutral300}`,
                  borderTop: `2px solid ${theme.colors.brandRed}`,
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
                Loading image...
              </div>
            </div>
          )}

          {!hasError && (
            <img
              src={src}
              alt={alt}
              style={{
                ...imageStyles,
                display: isLoading ? 'none' : 'block'
              }}
              onLoad={handleImageLoad}
              onError={handleImageError}
              onClick={toggleFullscreen}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.transform = 'scale(1.02)'
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.transform = 'scale(1)'
                }
              }}
              loading="lazy"
            />
          )}
        </div>
        
        {caption && (
          <div style={{
            padding: theme.spacing[3],
            fontSize: theme.fonts.sizes.sm,
            color: theme.colors.neutral600,
            borderTop: `1px solid ${theme.colors.neutral200}`,
          }}>
            {caption}
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div style={fullscreenStyles} onClick={toggleFullscreen}>
          <img
            src={src}
            alt={alt}
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              objectFit: 'contain',
            }}
          />
        </div>
      )}
    </>
  )
}

// Video Attachment Component
interface VideoAttachmentProps {
  src: string
  title?: string
  poster?: string
  controls?: boolean
  autoplay?: boolean
  muted?: boolean
}

export const VideoAttachment: React.FC<VideoAttachmentProps> = ({
  src,
  title,
  poster,
  controls = true,
  autoplay = false,
  muted = false
}) => {
  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    overflow: 'hidden',
    fontFamily: theme.fonts.families.primary,
  }

  return (
    <div style={containerStyles}>
      {title && (
        <div style={{
          padding: `${theme.spacing[3]} ${theme.spacing[3]} 0`,
          fontWeight: theme.fonts.weights.bold,
          color: theme.colors.brandRed,
        }}>
          {title}
        </div>
      )}
      
      <video
        src={src}
        poster={poster}
        controls={controls}
        autoPlay={autoplay}
        muted={muted}
        style={{
          width: '100%',
          maxWidth: '100%',
          height: 'auto',
        }}
      >
        Your browser does not support the video tag.
      </video>
    </div>
  )
}

// Audio Attachment Component
interface AudioAttachmentProps {
  src: string
  title?: string
  artist?: string
  duration?: string
}

export const AudioAttachment: React.FC<AudioAttachmentProps> = ({
  src,
  title,
  artist,
  duration
}) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)

  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    padding: theme.spacing[4],
    fontFamily: theme.fonts.families.primary,
  }

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  return (
    <div style={containerStyles}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing[3],
      }}>
        <button
          onClick={togglePlay}
          style={{
            width: '48px',
            height: '48px',
            borderRadius: '50%',
            border: 'none',
            background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
            color: theme.colors.neutralWhite,
            fontSize: '20px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.2s ease-in-out',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.1)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)'
          }}
        >
          {isPlaying ? '⏸️' : '▶️'}
        </button>
        
        <div style={{ flex: 1 }}>
          {title && (
            <div style={{
              fontWeight: theme.fonts.weights.bold,
              color: theme.colors.neutral800,
              marginBottom: theme.spacing[1],
            }}>
              {title}
            </div>
          )}
          
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            {artist && (
              <span style={{
                fontSize: theme.fonts.sizes.sm,
                color: theme.colors.neutral600,
              }}>
                {artist}
              </span>
            )}
            
            {duration && (
              <span style={{
                fontSize: theme.fonts.sizes.sm,
                color: theme.colors.neutral600,
              }}>
                {duration}
              </span>
            )}
          </div>
        </div>
      </div>
      
      <audio
        ref={audioRef}
        src={src}
        onEnded={() => setIsPlaying(false)}
        style={{ display: 'none' }}
      />
    </div>
  )
}

// File Download Component
interface FileAttachmentProps {
  src: string
  filename: string
  fileSize?: string
  fileType?: string
  description?: string
}

export const FileAttachment: React.FC<FileAttachmentProps> = ({
  src,
  filename,
  fileSize,
  fileType,
  description
}) => {
  const getFileIcon = (type?: string) => {
    if (!type) return '📄'
    
    if (type.includes('pdf')) return '📕'
    if (type.includes('word') || type.includes('doc')) return '📘'
    if (type.includes('excel') || type.includes('sheet')) return '📗'
    if (type.includes('powerpoint') || type.includes('presentation')) return '📙'
    if (type.includes('image')) return '🖼️'
    if (type.includes('video')) return '🎥'
    if (type.includes('audio')) return '🎵'
    if (type.includes('zip') || type.includes('archive')) return '📦'
    
    return '📄'
  }

  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    padding: theme.spacing[4],
    fontFamily: theme.fonts.families.primary,
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
  }

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = src
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div
      style={containerStyles}
      onClick={handleDownload}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = theme.colors.neutral50
        e.currentTarget.style.transform = 'translateY(-1px)'
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = theme.colors.neutralWhite
        e.currentTarget.style.transform = 'translateY(0)'
      }}
    >
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing[3],
      }}>
        <div style={{ fontSize: '32px' }}>
          {getFileIcon(fileType)}
        </div>
        
        <div style={{ flex: 1 }}>
          <div style={{
            fontWeight: theme.fonts.weights.bold,
            color: theme.colors.neutral800,
            marginBottom: theme.spacing[1],
          }}>
            {filename}
          </div>
          
          <div style={{
            display: 'flex',
            gap: theme.spacing[2],
            fontSize: theme.fonts.sizes.sm,
            color: theme.colors.neutral600,
          }}>
            {fileType && <span>{fileType}</span>}
            {fileSize && <span>• {fileSize}</span>}
          </div>
          
          {description && (
            <div style={{
              marginTop: theme.spacing[2],
              fontSize: theme.fonts.sizes.sm,
              color: theme.colors.neutral700,
            }}>
              {description}
            </div>
          )}
        </div>
        
        <div style={{
          color: theme.colors.brandRed,
          fontSize: '20px',
        }}>
          ⬇️
        </div>
      </div>
    </div>
  )
}

export default {
  ImageAttachment,
  VideoAttachment,
  AudioAttachment,
  FileAttachment,
}
