/**
 * Data Visualization Cards for Rich Bot Responses
 * Provides chart and graph components using Chart.js
 */

import React from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { Bar, Line, Doughnut } from 'react-chartjs-2'
import { theme } from '../../theme'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

// Common chart options with brand theming
const getChartOptions = (title?: string) => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        font: {
          family: theme.fonts.families.primary,
          size: 12,
        },
        color: theme.colors.neutral700,
      },
    },
    title: {
      display: !!title,
      text: title,
      font: {
        family: theme.fonts.families.primary,
        size: 16,
        weight: 'bold' as const,
      },
      color: theme.colors.brandRed,
    },
    tooltip: {
      backgroundColor: theme.colors.neutral800,
      titleColor: theme.colors.neutralWhite,
      bodyColor: theme.colors.neutralWhite,
      borderColor: theme.colors.neutral600,
      borderWidth: 1,
      cornerRadius: 6,
      titleFont: {
        family: theme.fonts.families.primary,
      },
      bodyFont: {
        family: theme.fonts.families.primary,
      },
    },
  },
  scales: {
    x: {
      grid: {
        color: theme.colors.neutral200,
      },
      ticks: {
        color: theme.colors.neutral600,
        font: {
          family: theme.fonts.families.primary,
        },
      },
    },
    y: {
      grid: {
        color: theme.colors.neutral200,
      },
      ticks: {
        color: theme.colors.neutral600,
        font: {
          family: theme.fonts.families.primary,
        },
      },
    },
  },
} as any)

// Chart Card Container
interface ChartCardProps {
  title?: string
  subtitle?: string
  children: React.ReactNode
  height?: number
}

const ChartCard: React.FC<ChartCardProps> = ({ title, subtitle, children, height = 300 }) => {
  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    padding: theme.spacing[4],
    fontFamily: theme.fonts.families.primary,
  }

  return (
    <div style={containerStyles}>
      {title && (
        <h3 style={{
          margin: `0 0 ${theme.spacing[2]} 0`,
          color: theme.colors.brandRed,
          fontSize: theme.fonts.sizes.lg,
          fontWeight: theme.fonts.weights.bold,
        }}>
          {title}
        </h3>
      )}
      
      {subtitle && (
        <p style={{
          margin: `0 0 ${theme.spacing[3]} 0`,
          color: theme.colors.neutral600,
          fontSize: theme.fonts.sizes.sm,
        }}>
          {subtitle}
        </p>
      )}
      
      <div style={{ height: `${height}px`, position: 'relative' }}>
        {children}
      </div>
    </div>
  )
}

// Bar Chart Component
interface BarChartCardProps {
  title?: string
  subtitle?: string
  data: {
    labels: string[]
    datasets: Array<{
      label: string
      data: number[]
      backgroundColor?: string
      borderColor?: string
    }>
  }
  height?: number
}

export const BarChartCard: React.FC<BarChartCardProps> = ({ title, subtitle, data, height }) => {
  const chartData = {
    ...data,
    datasets: data.datasets.map((dataset, index) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || (index === 0 ? theme.colors.brandRed : theme.colors.brandOrange),
      borderColor: dataset.borderColor || (index === 0 ? theme.colors.brandDarkRed : theme.colors.brandOrange),
      borderWidth: 1,
      borderRadius: 4,
    })),
  }

  return (
    <ChartCard title={title} subtitle={subtitle} height={height}>
      <Bar data={chartData} options={getChartOptions()} />
    </ChartCard>
  )
}

// Line Chart Component
interface LineChartCardProps {
  title?: string
  subtitle?: string
  data: {
    labels: string[]
    datasets: Array<{
      label: string
      data: number[]
      borderColor?: string
      backgroundColor?: string
    }>
  }
  height?: number
}

export const LineChartCard: React.FC<LineChartCardProps> = ({ title, subtitle, data, height }) => {
  const chartData = {
    ...data,
    datasets: data.datasets.map((dataset, index) => ({
      ...dataset,
      borderColor: dataset.borderColor || (index === 0 ? theme.colors.brandRed : theme.colors.brandOrange),
      backgroundColor: dataset.backgroundColor || (index === 0 ? `${theme.colors.brandRed}20` : `${theme.colors.brandOrange}20`),
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointBackgroundColor: dataset.borderColor || (index === 0 ? theme.colors.brandRed : theme.colors.brandOrange),
      pointBorderColor: theme.colors.neutralWhite,
      pointBorderWidth: 2,
      pointRadius: 4,
      pointHoverRadius: 6,
    })),
  }

  return (
    <ChartCard title={title} subtitle={subtitle} height={height}>
      <Line data={chartData} options={getChartOptions()} />
    </ChartCard>
  )
}

// Doughnut Chart Component
interface DoughnutChartCardProps {
  title?: string
  subtitle?: string
  data: {
    labels: string[]
    datasets: Array<{
      data: number[]
      backgroundColor?: string[]
      borderColor?: string[]
    }>
  }
  height?: number
}

export const DoughnutChartCard: React.FC<DoughnutChartCardProps> = ({ title, subtitle, data, height }) => {
  const defaultColors = [
    theme.colors.brandRed,
    theme.colors.brandOrange,
    theme.colors.brandGreen,
    theme.colors.brandBlue,
    theme.colors.neutral400,
  ]

  const chartData = {
    ...data,
    datasets: data.datasets.map((dataset) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || defaultColors,
      borderColor: dataset.borderColor || defaultColors.map(() => theme.colors.neutralWhite),
      borderWidth: 2,
    })),
  }

  const options = {
    ...getChartOptions(),
    scales: undefined, // Remove scales for doughnut chart
  }

  return (
    <ChartCard title={title} subtitle={subtitle} height={height}>
      <Doughnut data={chartData} options={options} />
    </ChartCard>
  )
}

// Progress Bar Component
interface ProgressBarCardProps {
  title?: string
  subtitle?: string
  items: Array<{
    label: string
    value: number
    maxValue: number
    color?: string
  }>
}

export const ProgressBarCard: React.FC<ProgressBarCardProps> = ({ title, subtitle, items }) => {
  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    padding: theme.spacing[4],
    fontFamily: theme.fonts.families.primary,
  }

  return (
    <div style={containerStyles}>
      {title && (
        <h3 style={{
          margin: `0 0 ${theme.spacing[2]} 0`,
          color: theme.colors.brandRed,
          fontSize: theme.fonts.sizes.lg,
          fontWeight: theme.fonts.weights.bold,
        }}>
          {title}
        </h3>
      )}
      
      {subtitle && (
        <p style={{
          margin: `0 0 ${theme.spacing[3]} 0`,
          color: theme.colors.neutral600,
          fontSize: theme.fonts.sizes.sm,
        }}>
          {subtitle}
        </p>
      )}
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing[3] }}>
        {items.map((item, index) => {
          const percentage = (item.value / item.maxValue) * 100
          const color = item.color || (index === 0 ? theme.colors.brandRed : theme.colors.brandOrange)
          
          return (
            <div key={index}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: theme.spacing[1],
              }}>
                <span style={{
                  fontSize: theme.fonts.sizes.sm,
                  fontWeight: theme.fonts.weights.medium,
                  color: theme.colors.neutral800,
                }}>
                  {item.label}
                </span>
                <span style={{
                  fontSize: theme.fonts.sizes.sm,
                  color: theme.colors.neutral600,
                }}>
                  {item.value}/{item.maxValue}
                </span>
              </div>
              
              <div style={{
                width: '100%',
                height: '8px',
                backgroundColor: theme.colors.neutral200,
                borderRadius: '4px',
                overflow: 'hidden',
              }}>
                <div style={{
                  width: `${percentage}%`,
                  height: '100%',
                  backgroundColor: color,
                  borderRadius: '4px',
                  transition: 'width 0.3s ease-in-out',
                }} />
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Metric Cards Component
interface MetricCardsProps {
  title?: string
  metrics: Array<{
    label: string
    value: string | number
    change?: {
      value: number
      type: 'increase' | 'decrease'
    }
    icon?: string
  }>
}

export const MetricCards: React.FC<MetricCardsProps> = ({ title, metrics }) => {
  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    padding: theme.spacing[4],
    fontFamily: theme.fonts.families.primary,
  }

  return (
    <div style={containerStyles}>
      {title && (
        <h3 style={{
          margin: `0 0 ${theme.spacing[3]} 0`,
          color: theme.colors.brandRed,
          fontSize: theme.fonts.sizes.lg,
          fontWeight: theme.fonts.weights.bold,
        }}>
          {title}
        </h3>
      )}
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
        gap: theme.spacing[3],
      }}>
        {metrics.map((metric, index) => (
          <div key={index} style={{
            padding: theme.spacing[3],
            backgroundColor: theme.colors.neutral50,
            borderRadius: '6px',
            border: `1px solid ${theme.colors.neutral200}`,
            textAlign: 'center',
          }}>
            {metric.icon && (
              <div style={{
                fontSize: '24px',
                marginBottom: theme.spacing[2],
              }}>
                {metric.icon}
              </div>
            )}
            
            <div style={{
              fontSize: theme.fonts.sizes['2xl'],
              fontWeight: theme.fonts.weights.bold,
              color: theme.colors.brandRed,
              marginBottom: theme.spacing[1],
            }}>
              {metric.value}
            </div>
            
            <div style={{
              fontSize: theme.fonts.sizes.sm,
              color: theme.colors.neutral600,
              marginBottom: metric.change ? theme.spacing[1] : 0,
            }}>
              {metric.label}
            </div>
            
            {metric.change && (
              <div style={{
                fontSize: theme.fonts.sizes.xs,
                color: metric.change.type === 'increase' ? theme.colors.brandGreen : theme.colors.error,
                fontWeight: theme.fonts.weights.medium,
              }}>
                {metric.change.type === 'increase' ? '↗' : '↘'} {Math.abs(metric.change.value)}%
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default {
  BarChartCard,
  LineChartCard,
  DoughnutChartCard,
  ProgressBarCard,
  MetricCards,
}
