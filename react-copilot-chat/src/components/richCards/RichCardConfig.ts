/**
 * Rich Card Configuration for Microsoft Bot Framework WebChat
 * Configures support for Adaptive Cards, Hero Cards, and custom rich content
 */

import { AdaptiveCard } from 'adaptivecards'
import { theme } from '../../theme'

// Adaptive Cards Host Config
export const adaptiveCardHostConfig = {
  spacing: {
    small: parseInt(theme.spacing[1]),
    default: parseInt(theme.spacing[2]),
    medium: parseInt(theme.spacing[3]),
    large: parseInt(theme.spacing[4]),
    extraLarge: parseInt(theme.spacing[6]),
    padding: parseInt(theme.spacing[3])
  },
  separator: {
    lineThickness: 1,
    lineColor: theme.colors.neutral200
  },
  supportsInteractivity: true,
  fontTypes: {
    default: {
      fontFamily: theme.fonts.families.primary,
      fontSizes: {
        small: parseInt(theme.fonts.sizes.sm.replace('px', '')),
        default: parseInt(theme.fonts.sizes.base.replace('px', '')),
        medium: parseInt(theme.fonts.sizes.lg.replace('px', '')),
        large: parseInt(theme.fonts.sizes.xl.replace('px', '')),
        extraLarge: parseInt(theme.fonts.sizes['2xl'].replace('px', ''))
      },
      fontWeights: {
        lighter: 300,
        default: theme.fonts.weights.normal,
        bolder: theme.fonts.weights.bold
      }
    },
    monospace: {
      fontFamily: 'Monaco, Consolas, "Courier New", monospace'
    }
  },
  containerStyles: {
    default: {
      backgroundColor: theme.colors.neutralWhite,
      foregroundColors: {
        default: {
          default: theme.colors.neutral800,
          subtle: theme.colors.neutral600
        },
        accent: {
          default: theme.colors.brandRed,
          subtle: theme.colors.brandLightRed
        },
        attention: {
          default: theme.colors.error,
          subtle: theme.colors.brandLightRed
        },
        good: {
          default: theme.colors.brandGreen,
          subtle: theme.colors.success
        },
        warning: {
          default: theme.colors.warning,
          subtle: theme.colors.brandOrange
        }
      }
    },
    emphasis: {
      backgroundColor: theme.colors.neutral50,
      foregroundColors: {
        default: {
          default: theme.colors.neutral800,
          subtle: theme.colors.neutral600
        }
      }
    }
  },
  imageSizes: {
    small: 40,
    medium: 80,
    large: 160
  },
  actions: {
    maxActions: 5,
    spacing: 'default',
    buttonSpacing: parseInt(theme.spacing[2]),
    showCard: {
      actionMode: 'inline',
      inlineTopMargin: parseInt(theme.spacing[3])
    },
    actionsOrientation: 'horizontal',
    actionAlignment: 'left'
  },
  adaptiveCard: {
    allowCustomStyle: true
  }
}

// WebChat Activity Middleware Configuration
const activityMiddleware = () => (next: any) => (card: any) => {
  const { activity } = card
  
  // Handle different attachment types
  if (activity.attachments && activity.attachments.length > 0) {
    const attachment = activity.attachments[0]
    
    switch (attachment.contentType) {
      case 'application/vnd.microsoft.card.adaptive':
        return renderAdaptiveCard(attachment, card)
      case 'application/vnd.microsoft.card.hero':
        return renderHeroCard(attachment, card)
      case 'application/vnd.microsoft.card.thumbnail':
        return renderThumbnailCard(attachment, card)
      case 'application/vnd.microsoft.card.receipt':
        return renderReceiptCard(attachment, card)
      case 'application/vnd.microsoft.card.signin':
        return renderSignInCard(attachment, card)
      default:
        return next(card)
    }
  }
  
  return next(card)
}

// Adaptive Card Renderer
function renderAdaptiveCard(attachment: any, card: any) {
  const adaptiveCard = new AdaptiveCard()
  adaptiveCard.hostConfig = adaptiveCardHostConfig as any
  
  try {
    adaptiveCard.parse(attachment.content)
    const renderedCard = adaptiveCard.render()
    
    if (renderedCard) {
      // Apply custom styling
      renderedCard.style.fontFamily = theme.fonts.families.primary
      renderedCard.style.borderRadius = '8px'
      renderedCard.style.border = `1px solid ${theme.colors.neutral200}`
      renderedCard.style.padding = theme.spacing[3]
      renderedCard.style.margin = `${theme.spacing[2]} 0`
      renderedCard.style.backgroundColor = theme.colors.neutralWhite
      renderedCard.style.boxShadow = theme.shadows.small
      
      return () => {
        const container = document.createElement('div')
        container.appendChild(renderedCard)
        return container
      }
    }
  } catch (error) {
    console.error('Error rendering Adaptive Card:', error)
  }
  
  return card
}

// Hero Card Renderer
function renderHeroCard(attachment: any, _card: any) {
  const content = attachment.content
  
  return () => {
    const container = document.createElement('div')
    container.style.cssText = `
      border: 1px solid ${theme.colors.neutral200};
      border-radius: 8px;
      padding: ${theme.spacing[4]};
      margin: ${theme.spacing[2]} 0;
      background-color: ${theme.colors.neutralWhite};
      box-shadow: ${theme.shadows.small};
      font-family: ${theme.fonts.families.primary};
    `
    
    // Title
    if (content.title) {
      const title = document.createElement('h3')
      title.textContent = content.title
      title.style.cssText = `
        margin: 0 0 ${theme.spacing[2]} 0;
        color: ${theme.colors.brandRed};
        font-size: ${theme.fonts.sizes.lg};
        font-weight: ${theme.fonts.weights.bold};
      `
      container.appendChild(title)
    }
    
    // Subtitle
    if (content.subtitle) {
      const subtitle = document.createElement('p')
      subtitle.textContent = content.subtitle
      subtitle.style.cssText = `
        margin: 0 0 ${theme.spacing[2]} 0;
        color: ${theme.colors.neutral600};
        font-size: ${theme.fonts.sizes.sm};
      `
      container.appendChild(subtitle)
    }
    
    // Image
    if (content.images && content.images.length > 0) {
      const img = document.createElement('img')
      img.src = content.images[0].url
      img.alt = content.images[0].alt || 'Card image'
      img.style.cssText = `
        width: 100%;
        max-width: 300px;
        height: auto;
        border-radius: 6px;
        margin: ${theme.spacing[2]} 0;
      `
      container.appendChild(img)
    }
    
    // Text
    if (content.text) {
      const text = document.createElement('p')
      text.textContent = content.text
      text.style.cssText = `
        margin: 0 0 ${theme.spacing[3]} 0;
        color: ${theme.colors.neutral800};
        line-height: ${theme.fonts.lineHeights.normal};
      `
      container.appendChild(text)
    }
    
    // Buttons
    if (content.buttons && content.buttons.length > 0) {
      const buttonContainer = document.createElement('div')
      buttonContainer.style.cssText = `
        display: flex;
        gap: ${theme.spacing[2]};
        flex-wrap: wrap;
        margin-top: ${theme.spacing[3]};
      `
      
      content.buttons.forEach((button: any) => {
        const btn = document.createElement('button')
        btn.textContent = button.title
        btn.style.cssText = `
          background: linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%);
          color: ${theme.colors.neutralWhite};
          border: none;
          padding: ${theme.spacing[2]} ${theme.spacing[4]};
          border-radius: 6px;
          font-size: ${theme.fonts.sizes.sm};
          font-weight: ${theme.fonts.weights.medium};
          cursor: pointer;
          transition: all 0.2s ease-in-out;
        `
        
        btn.addEventListener('click', () => {
          if (button.type === 'openUrl' && button.value) {
            window.open(button.value, '_blank')
          } else if (button.type === 'imBack' && button.value) {
            // Send message back to bot
            console.log('Button clicked:', button.value)
          }
        })
        
        btn.addEventListener('mouseenter', () => {
          btn.style.transform = 'translateY(-1px)'
          btn.style.boxShadow = theme.shadows.medium
        })
        
        btn.addEventListener('mouseleave', () => {
          btn.style.transform = 'translateY(0)'
          btn.style.boxShadow = 'none'
        })
        
        buttonContainer.appendChild(btn)
      })
      
      container.appendChild(buttonContainer)
    }
    
    return container
  }
}

// Thumbnail Card Renderer
function renderThumbnailCard(attachment: any, card: any) {
  // Similar to hero card but with smaller image
  return renderHeroCard(attachment, card)
}

// Receipt Card Renderer
function renderReceiptCard(_attachment: any, card: any) {
  // Implementation for receipt cards
  return card
}

// Sign-in Card Renderer
function renderSignInCard(_attachment: any, card: any) {
  // Implementation for sign-in cards
  return card
}

export { activityMiddleware }
