/**
 * Rich Card Styling and Theming
 * Provides consistent styling for all rich card components with responsive design
 */

import React from 'react'
import { theme } from '../../theme'

// Base card styles
export const baseCardStyles: React.CSSProperties = {
  border: `1px solid ${theme.colors.neutral200}`,
  borderRadius: '8px',
  margin: `${theme.spacing[2]} 0`,
  backgroundColor: theme.colors.neutralWhite,
  boxShadow: theme.shadows.small,
  fontFamily: theme.fonts.families.primary,
  overflow: 'hidden',
  transition: 'all 0.2s ease-in-out',
}

// Enhanced card styles with hover effects
export const interactiveCardStyles: React.CSSProperties = {
  ...baseCardStyles,
  cursor: 'pointer',
}

// Card header styles
export const cardHeaderStyles: React.CSSProperties = {
  padding: theme.spacing[4],
  borderBottom: `1px solid ${theme.colors.neutral200}`,
  backgroundColor: theme.colors.neutral50,
}

// Card body styles
export const cardBodyStyles: React.CSSProperties = {
  padding: theme.spacing[4],
}

// Card footer styles
export const cardFooterStyles: React.CSSProperties = {
  padding: theme.spacing[3],
  borderTop: `1px solid ${theme.colors.neutral200}`,
  backgroundColor: theme.colors.neutral50,
}

// Title styles
export const cardTitleStyles: React.CSSProperties = {
  margin: 0,
  color: theme.colors.brandRed,
  fontSize: theme.fonts.sizes.lg,
  fontWeight: theme.fonts.weights.bold,
  lineHeight: theme.fonts.lineHeights.tight,
}

// Subtitle styles
export const cardSubtitleStyles: React.CSSProperties = {
  margin: `${theme.spacing[1]} 0 0 0`,
  color: theme.colors.neutral600,
  fontSize: theme.fonts.sizes.sm,
  lineHeight: theme.fonts.lineHeights.normal,
}

// Text content styles
export const cardTextStyles: React.CSSProperties = {
  margin: `${theme.spacing[3]} 0 0 0`,
  color: theme.colors.neutral800,
  fontSize: theme.fonts.sizes.base,
  lineHeight: theme.fonts.lineHeights.relaxed,
}

// Button styles
export const primaryButtonStyles: React.CSSProperties = {
  background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
  color: theme.colors.neutralWhite,
  border: 'none',
  padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
  borderRadius: '6px',
  fontSize: theme.fonts.sizes.sm,
  fontWeight: theme.fonts.weights.medium,
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  textDecoration: 'none',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing[1],
}

export const secondaryButtonStyles: React.CSSProperties = {
  background: 'transparent',
  color: theme.colors.brandRed,
  border: `1px solid ${theme.colors.brandRed}`,
  padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
  borderRadius: '6px',
  fontSize: theme.fonts.sizes.sm,
  fontWeight: theme.fonts.weights.medium,
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  textDecoration: 'none',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing[1],
}

// Button container styles
export const buttonContainerStyles: React.CSSProperties = {
  display: 'flex',
  gap: theme.spacing[2],
  flexWrap: 'wrap',
  marginTop: theme.spacing[3],
}

// Image styles
export const cardImageStyles: React.CSSProperties = {
  width: '100%',
  height: 'auto',
  maxHeight: '300px',
  objectFit: 'cover',
  borderRadius: '6px',
}

// Responsive breakpoints
export const breakpoints = {
  mobile: '768px',
  tablet: '1024px',
  desktop: '1200px',
}

// Responsive card styles
export const getResponsiveCardStyles = (isFloating: boolean): React.CSSProperties => {
  const baseStyles = { ...baseCardStyles }
  
  if (isFloating) {
    // Floating mode adjustments
    return {
      ...baseStyles,
      margin: `${theme.spacing[1]} 0`,
      borderRadius: '6px',
    }
  }
  
  return baseStyles
}

// Mobile-specific styles
export const mobileCardStyles: React.CSSProperties = {
  ...baseCardStyles,
  margin: `${theme.spacing[1]} 0`,
  borderRadius: '6px',
}

// Button hover effects
export const addButtonHoverEffects = (element: HTMLElement, isPrimary: boolean = true) => {
  element.addEventListener('mouseenter', () => {
    element.style.transform = 'translateY(-1px)'
    element.style.boxShadow = theme.shadows.medium
    
    if (!isPrimary) {
      element.style.backgroundColor = theme.colors.brandRed
      element.style.color = theme.colors.neutralWhite
    }
  })
  
  element.addEventListener('mouseleave', () => {
    element.style.transform = 'translateY(0)'
    element.style.boxShadow = 'none'
    
    if (!isPrimary) {
      element.style.backgroundColor = 'transparent'
      element.style.color = theme.colors.brandRed
    }
  })
}

// Card hover effects
export const addCardHoverEffects = (element: HTMLElement) => {
  element.addEventListener('mouseenter', () => {
    element.style.transform = 'translateY(-2px)'
    element.style.boxShadow = theme.shadows.medium
  })
  
  element.addEventListener('mouseleave', () => {
    element.style.transform = 'translateY(0)'
    element.style.boxShadow = theme.shadows.small
  })
}

// Accessibility styles
export const accessibilityStyles = {
  focusOutline: `2px solid ${theme.colors.brandRed}`,
  focusOutlineOffset: '2px',
  screenReaderOnly: {
    position: 'absolute' as const,
    width: '1px',
    height: '1px',
    padding: 0,
    margin: '-1px',
    overflow: 'hidden' as const,
    clip: 'rect(0, 0, 0, 0)',
    whiteSpace: 'nowrap' as const,
    border: 0,
  },
}

// Animation styles
export const animationStyles = {
  fadeIn: {
    animation: 'fadeIn 0.3s ease-in-out',
  },
  slideUp: {
    animation: 'slideUp 0.3s ease-out',
  },
  scaleIn: {
    animation: 'scaleIn 0.2s ease-out',
  },
}

// CSS animations (to be added to global styles)
export const cssAnimations = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0;
      transform: translateY(20px);
    }
    to { 
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes scaleIn {
    from { 
      opacity: 0;
      transform: scale(0.95);
    }
    to { 
      opacity: 1;
      transform: scale(1);
    }
  }
  
  /* Responsive styles */
  @media (max-width: ${breakpoints.mobile}) {
    .rich-card {
      margin: ${theme.spacing[1]} 0;
      border-radius: 6px;
    }
    
    .rich-card-buttons {
      flex-direction: column;
      align-items: stretch;
    }
    
    .rich-card-buttons button {
      width: 100%;
      margin-bottom: ${theme.spacing[1]};
    }
    
    .rich-card-image {
      max-height: 200px;
    }
    
    .rich-card-carousel {
      height: auto;
    }
    
    .rich-card-carousel .navigation-button {
      display: none;
    }
  }
  
  @media (max-width: 480px) {
    .rich-card {
      margin: ${theme.spacing[1]} 0;
      border-radius: 4px;
    }
    
    .rich-card-title {
      font-size: ${theme.fonts.sizes.base};
    }
    
    .rich-card-text {
      font-size: ${theme.fonts.sizes.sm};
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .rich-card {
      border: 2px solid currentColor;
    }
    
    .rich-card-button {
      border: 2px solid currentColor;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .rich-card,
    .rich-card-button,
    .rich-card-carousel {
      transition: none;
      animation: none;
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .rich-card {
      background-color: #1f2937;
      border-color: #374151;
      color: #f9fafb;
    }
    
    .rich-card-header,
    .rich-card-footer {
      background-color: #111827;
    }
  }
`

// Utility functions for dynamic styling
export const getCardVariant = (variant: 'default' | 'elevated' | 'outlined' | 'filled') => {
  switch (variant) {
    case 'elevated':
      return {
        ...baseCardStyles,
        boxShadow: theme.shadows.large,
        border: 'none',
      }
    case 'outlined':
      return {
        ...baseCardStyles,
        border: `2px solid ${theme.colors.brandRed}`,
        boxShadow: 'none',
      }
    case 'filled':
      return {
        ...baseCardStyles,
        backgroundColor: theme.colors.neutral50,
        border: `1px solid ${theme.colors.neutral300}`,
      }
    default:
      return baseCardStyles
  }
}

export const getButtonVariant = (variant: 'primary' | 'secondary' | 'outline' | 'ghost') => {
  switch (variant) {
    case 'secondary':
      return {
        ...secondaryButtonStyles,
        backgroundColor: theme.colors.neutral100,
        color: theme.colors.neutral800,
        border: `1px solid ${theme.colors.neutral300}`,
      }
    case 'outline':
      return secondaryButtonStyles
    case 'ghost':
      return {
        ...secondaryButtonStyles,
        border: 'none',
        backgroundColor: 'transparent',
      }
    default:
      return primaryButtonStyles
  }
}

// Export all styles
export default {
  baseCardStyles,
  interactiveCardStyles,
  cardHeaderStyles,
  cardBodyStyles,
  cardFooterStyles,
  cardTitleStyles,
  cardSubtitleStyles,
  cardTextStyles,
  primaryButtonStyles,
  secondaryButtonStyles,
  buttonContainerStyles,
  cardImageStyles,
  getResponsiveCardStyles,
  mobileCardStyles,
  addButtonHoverEffects,
  addCardHoverEffects,
  accessibilityStyles,
  animationStyles,
  cssAnimations,
  getCardVariant,
  getButtonVariant,
}
