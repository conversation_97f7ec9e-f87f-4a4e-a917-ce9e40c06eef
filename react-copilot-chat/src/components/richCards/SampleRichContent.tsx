/**
 * Sample Rich Content Examples
 * Provides examples and test data for rich card rendering
 */

import React from 'react'
import { CarouselCard, InteractiveListCard } from './CustomCardRenderers'
import { BarChartCard, LineChartCard, DoughnutChartCard, ProgressBarCard, MetricCards } from './DataVisualizationCards'
import { ImageAttachment, AudioAttachment, FileAttachment } from './MediaAttachments'

// Sample data for demonstrations
export const sampleCarouselData = [
  {
    title: "Product Showcase",
    subtitle: "Discover our latest offerings",
    text: "Explore our premium product line with advanced features and competitive pricing.",
    images: [{ url: "https://via.placeholder.com/300x200/E5384C/FFFFFF?text=Product+1", alt: "Product 1" }],
    buttons: [
      { title: "Learn More", type: "openUrl", value: "https://example.com/product1" },
      { title: "Buy Now", type: "postBack", value: "buy_product_1" }
    ]
  },
  {
    title: "Service Excellence",
    subtitle: "24/7 customer support",
    text: "Our dedicated team is here to help you succeed with round-the-clock assistance.",
    images: [{ url: "https://via.placeholder.com/300x200/EA714F/FFFFFF?text=Service", alt: "Service" }],
    buttons: [
      { title: "Contact Support", type: "openUrl", value: "https://example.com/support" },
      { title: "Schedule Call", type: "postBack", value: "schedule_call" }
    ]
  },
  {
    title: "Innovation Hub",
    subtitle: "Cutting-edge technology",
    text: "Stay ahead with our innovative solutions designed for the future of business.",
    images: [{ url: "https://via.placeholder.com/300x200/009b65/FFFFFF?text=Innovation", alt: "Innovation" }],
    buttons: [
      { title: "Explore Tech", type: "openUrl", value: "https://example.com/innovation" },
      { title: "Request Demo", type: "postBack", value: "request_demo" }
    ]
  }
]

export const sampleListData = {
  title: "Quick Actions",
  items: [
    {
      title: "View Account Balance",
      subtitle: "Check your current balance and recent transactions",
      image: "https://via.placeholder.com/48x48/E5384C/FFFFFF?text=$",
      value: "view_balance",
      type: "postBack" as const
    },
    {
      title: "Transfer Money",
      subtitle: "Send money to friends and family",
      image: "https://via.placeholder.com/48x48/EA714F/FFFFFF?text=→",
      value: "transfer_money",
      type: "postBack" as const
    },
    {
      title: "Contact Support",
      subtitle: "Get help from our customer service team",
      image: "https://via.placeholder.com/48x48/009b65/FFFFFF?text=?",
      value: "tel:+**********",
      type: "call" as const
    },
    {
      title: "Visit Website",
      subtitle: "Explore more features on our website",
      image: "https://via.placeholder.com/48x48/0078d4/FFFFFF?text=🌐",
      value: "https://example.com",
      type: "openUrl" as const
    }
  ]
}

export const sampleChartData = {
  bar: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Sales',
        data: [12, 19, 3, 5, 2, 3],
      },
      {
        label: 'Revenue',
        data: [8, 15, 7, 12, 9, 6],
      }
    ]
  },
  line: {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [
      {
        label: 'User Growth',
        data: [100, 150, 200, 280],
      }
    ]
  },
  doughnut: {
    labels: ['Desktop', 'Mobile', 'Tablet'],
    datasets: [
      {
        data: [60, 35, 5],
      }
    ]
  }
}

export const sampleProgressData = [
  { label: 'Project Alpha', value: 75, maxValue: 100 },
  { label: 'Project Beta', value: 45, maxValue: 100 },
  { label: 'Project Gamma', value: 90, maxValue: 100 },
]

export const sampleMetrics = [
  {
    label: 'Total Users',
    value: '12,345',
    change: { value: 12, type: 'increase' as const },
    icon: '👥'
  },
  {
    label: 'Revenue',
    value: '$45,678',
    change: { value: 8, type: 'increase' as const },
    icon: '💰'
  },
  {
    label: 'Conversion Rate',
    value: '3.2%',
    change: { value: 2, type: 'decrease' as const },
    icon: '📈'
  },
  {
    label: 'Satisfaction',
    value: '4.8/5',
    change: { value: 5, type: 'increase' as const },
    icon: '⭐'
  }
]

// Sample Rich Content Demo Component
export const SampleRichContentDemo: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'system-ui, -apple-system, sans-serif',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1 style={{ color: '#E5384C', marginBottom: '30px' }}>
        🎨 Rich Content Examples
      </h1>
      
      <p style={{ marginBottom: '30px', color: '#666' }}>
        These examples demonstrate the rich content capabilities of our enhanced WebChat interface.
        In a real bot conversation, these would be sent as structured attachments from your bot service.
      </p>

      {/* Carousel Example */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#E5384C', marginBottom: '20px' }}>📱 Carousel Cards</h2>
        <CarouselCard cards={sampleCarouselData} />
      </section>

      {/* Interactive List Example */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#E5384C', marginBottom: '20px' }}>📋 Interactive List</h2>
        <InteractiveListCard {...sampleListData} />
      </section>

      {/* Charts Examples */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#E5384C', marginBottom: '20px' }}>📊 Data Visualizations</h2>
        
        <BarChartCard
          title="Sales Performance"
          subtitle="Monthly sales and revenue comparison"
          data={sampleChartData.bar}
          height={250}
        />
        
        <LineChartCard
          title="User Growth Trend"
          subtitle="Weekly user acquisition over the past month"
          data={sampleChartData.line}
          height={200}
        />
        
        <DoughnutChartCard
          title="Device Usage"
          subtitle="Traffic distribution by device type"
          data={sampleChartData.doughnut}
          height={250}
        />
      </section>

      {/* Progress and Metrics */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#E5384C', marginBottom: '20px' }}>📈 Progress & Metrics</h2>
        
        <ProgressBarCard
          title="Project Status"
          subtitle="Current progress on active projects"
          items={sampleProgressData}
        />
        
        <MetricCards
          title="Key Performance Indicators"
          metrics={sampleMetrics}
        />
      </section>

      {/* Media Examples */}
      <section style={{ marginBottom: '40px' }}>
        <h2 style={{ color: '#E5384C', marginBottom: '20px' }}>🎬 Media Attachments</h2>
        
        <ImageAttachment
          src="https://via.placeholder.com/400x300/E5384C/FFFFFF?text=Sample+Image"
          title="Product Image"
          caption="High-quality product photography showcasing our latest design"
          alt="Sample product image"
        />
        
        <AudioAttachment
          src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav"
          title="Welcome Message"
          artist="Customer Service"
          duration="0:15"
        />
        
        <FileAttachment
          src="https://example.com/sample.pdf"
          filename="Product_Catalog_2024.pdf"
          fileSize="2.3 MB"
          fileType="application/pdf"
          description="Complete product catalog with specifications and pricing"
        />
      </section>

      {/* Bot Integration Instructions */}
      <section style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #e9ecef',
        marginBottom: '40px'
      }}>
        <h2 style={{ color: '#E5384C', marginBottom: '15px' }}>🤖 Bot Integration</h2>
        <p style={{ marginBottom: '15px', color: '#666' }}>
          To send rich content from your bot, use the Bot Framework's attachment system:
        </p>
        <pre style={{
          backgroundColor: '#f1f3f4',
          padding: '15px',
          borderRadius: '6px',
          overflow: 'auto',
          fontSize: '14px',
          fontFamily: 'Monaco, Consolas, monospace'
        }}>
{`// Example: Sending a Hero Card
const heroCard = {
  contentType: 'application/vnd.microsoft.card.hero',
  content: {
    title: 'Welcome!',
    subtitle: 'How can I help you today?',
    text: 'Choose from the options below or ask me anything.',
    images: [{ url: 'https://example.com/image.jpg' }],
    buttons: [
      { type: 'postBack', title: 'Get Started', value: 'get_started' },
      { type: 'openUrl', title: 'Learn More', value: 'https://example.com' }
    ]
  }
}

// Send as attachment
await context.sendActivity({
  attachments: [heroCard]
})`}
        </pre>
      </section>
    </div>
  )
}

export default SampleRichContentDemo
