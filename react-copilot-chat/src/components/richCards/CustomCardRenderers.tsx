/**
 * Custom Card Renderers for Rich Interactive Elements
 * Provides React components for carousels, interactive cards, and custom layouts
 */

import React, { useState, useRef, useEffect } from 'react'
import { theme } from '../../theme'

// Carousel Card Component
interface CarouselCardProps {
  cards: Array<{
    title?: string
    subtitle?: string
    text?: string
    images?: Array<{ url: string; alt?: string }>
    buttons?: Array<{ title: string; type: string; value: string }>
  }>
}

export const CarouselCard: React.FC<CarouselCardProps> = ({ cards }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const carouselRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % cards.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [cards.length, isAutoPlaying])

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + cards.length) % cards.length)
    setIsAutoPlaying(false)
  }

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % cards.length)
    setIsAutoPlaying(false)
  }

  const containerStyles: React.CSSProperties = {
    position: 'relative',
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    overflow: 'hidden',
    fontFamily: theme.fonts.families.primary,
  }

  const slideStyles: React.CSSProperties = {
    display: 'flex',
    transform: `translateX(-${currentIndex * 100}%)`,
    transition: 'transform 0.3s ease-in-out',
  }

  const cardStyles: React.CSSProperties = {
    minWidth: '100%',
    padding: theme.spacing[4],
  }

  const navigationStyles: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    background: `rgba(0, 0, 0, 0.5)`,
    color: theme.colors.neutralWhite,
    border: 'none',
    borderRadius: '50%',
    width: '40px',
    height: '40px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '18px',
    transition: 'all 0.2s ease-in-out',
  }

  const indicatorContainerStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    gap: theme.spacing[1],
    padding: theme.spacing[2],
    backgroundColor: theme.colors.neutral50,
  }

  const indicatorStyles = (isActive: boolean): React.CSSProperties => ({
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    backgroundColor: isActive ? theme.colors.brandRed : theme.colors.neutral300,
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
  })

  return (
    <div style={containerStyles} ref={carouselRef}>
      <div style={{ overflow: 'hidden' }}>
        <div style={slideStyles}>
          {cards.map((card, index) => (
            <div key={index} style={cardStyles}>
              {card.title && (
                <h3 style={{
                  margin: `0 0 ${theme.spacing[2]} 0`,
                  color: theme.colors.brandRed,
                  fontSize: theme.fonts.sizes.lg,
                  fontWeight: theme.fonts.weights.bold,
                }}>
                  {card.title}
                </h3>
              )}
              
              {card.subtitle && (
                <p style={{
                  margin: `0 0 ${theme.spacing[2]} 0`,
                  color: theme.colors.neutral600,
                  fontSize: theme.fonts.sizes.sm,
                }}>
                  {card.subtitle}
                </p>
              )}
              
              {card.images && card.images.length > 0 && (
                <img
                  src={card.images[0].url}
                  alt={card.images[0].alt || 'Card image'}
                  style={{
                    width: '100%',
                    maxWidth: '300px',
                    height: 'auto',
                    borderRadius: '6px',
                    margin: `${theme.spacing[2]} 0`,
                  }}
                />
              )}
              
              {card.text && (
                <p style={{
                  margin: `0 0 ${theme.spacing[3]} 0`,
                  color: theme.colors.neutral800,
                  lineHeight: theme.fonts.lineHeights.normal,
                }}>
                  {card.text}
                </p>
              )}
              
              {card.buttons && card.buttons.length > 0 && (
                <div style={{
                  display: 'flex',
                  gap: theme.spacing[2],
                  flexWrap: 'wrap',
                  marginTop: theme.spacing[3],
                }}>
                  {card.buttons.map((button, btnIndex) => (
                    <button
                      key={btnIndex}
                      style={{
                        background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
                        color: theme.colors.neutralWhite,
                        border: 'none',
                        padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
                        borderRadius: '6px',
                        fontSize: theme.fonts.sizes.sm,
                        fontWeight: theme.fonts.weights.medium,
                        cursor: 'pointer',
                        transition: 'all 0.2s ease-in-out',
                      }}
                      onClick={() => {
                        if (button.type === 'openUrl' && button.value) {
                          window.open(button.value, '_blank')
                        }
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-1px)'
                        e.currentTarget.style.boxShadow = theme.shadows.medium
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)'
                        e.currentTarget.style.boxShadow = 'none'
                      }}
                    >
                      {button.title}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      {cards.length > 1 && (
        <>
          <button
            style={{ ...navigationStyles, left: theme.spacing[2] }}
            onClick={goToPrevious}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.7)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.5)'
            }}
            aria-label="Previous card"
          >
            ‹
          </button>
          <button
            style={{ ...navigationStyles, right: theme.spacing[2] }}
            onClick={goToNext}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.7)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.5)'
            }}
            aria-label="Next card"
          >
            ›
          </button>
        </>
      )}

      {/* Indicators */}
      {cards.length > 1 && (
        <div style={indicatorContainerStyles}>
          {cards.map((_, index) => (
            <div
              key={index}
              style={indicatorStyles(index === currentIndex)}
              onClick={() => goToSlide(index)}
              onMouseEnter={(e) => {
                if (index !== currentIndex) {
                  e.currentTarget.style.backgroundColor = theme.colors.neutral400
                }
              }}
              onMouseLeave={(e) => {
                if (index !== currentIndex) {
                  e.currentTarget.style.backgroundColor = theme.colors.neutral300
                }
              }}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Interactive List Card Component
interface InteractiveListCardProps {
  title?: string
  items: Array<{
    title: string
    subtitle?: string
    image?: string
    value: string
    type: 'postBack' | 'openUrl' | 'call'
  }>
}

export const InteractiveListCard: React.FC<InteractiveListCardProps> = ({ title, items }) => {
  const containerStyles: React.CSSProperties = {
    border: `1px solid ${theme.colors.neutral200}`,
    borderRadius: '8px',
    margin: `${theme.spacing[2]} 0`,
    backgroundColor: theme.colors.neutralWhite,
    boxShadow: theme.shadows.small,
    overflow: 'hidden',
    fontFamily: theme.fonts.families.primary,
  }

  const headerStyles: React.CSSProperties = {
    padding: theme.spacing[3],
    backgroundColor: theme.colors.neutral50,
    borderBottom: `1px solid ${theme.colors.neutral200}`,
  }

  const itemStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    padding: theme.spacing[3],
    borderBottom: `1px solid ${theme.colors.neutral100}`,
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
  }

  const handleItemClick = (item: any) => {
    switch (item.type) {
      case 'openUrl':
        window.open(item.value, '_blank')
        break
      case 'call':
        window.location.href = `tel:${item.value}`
        break
      case 'postBack':
        console.log('PostBack:', item.value)
        break
    }
  }

  return (
    <div style={containerStyles}>
      {title && (
        <div style={headerStyles}>
          <h3 style={{
            margin: 0,
            color: theme.colors.brandRed,
            fontSize: theme.fonts.sizes.lg,
            fontWeight: theme.fonts.weights.bold,
          }}>
            {title}
          </h3>
        </div>
      )}
      
      <div>
        {items.map((item, index) => (
          <div
            key={index}
            style={itemStyles}
            onClick={() => handleItemClick(item)}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = theme.colors.neutral50
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            {item.image && (
              <img
                src={item.image}
                alt={item.title}
                style={{
                  width: '48px',
                  height: '48px',
                  borderRadius: '6px',
                  marginRight: theme.spacing[3],
                  objectFit: 'cover',
                }}
              />
            )}
            
            <div style={{ flex: 1 }}>
              <div style={{
                fontWeight: theme.fonts.weights.medium,
                color: theme.colors.neutral800,
                marginBottom: item.subtitle ? theme.spacing[1] : 0,
              }}>
                {item.title}
              </div>
              
              {item.subtitle && (
                <div style={{
                  fontSize: theme.fonts.sizes.sm,
                  color: theme.colors.neutral600,
                }}>
                  {item.subtitle}
                </div>
              )}
            </div>
            
            <div style={{
              color: theme.colors.brandRed,
              fontSize: '18px',
              marginLeft: theme.spacing[2],
            }}>
              {item.type === 'openUrl' ? '🔗' : item.type === 'call' ? '📞' : '💬'}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default { CarouselCard, InteractiveListCard }
