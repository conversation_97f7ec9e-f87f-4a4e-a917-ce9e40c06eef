// Main entry point for the React application
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App'

const rootElement = document.getElementById('root')
if (!rootElement) {
  throw new Error('Root element not found')
}

createRoot(rootElement).render(
  // Temporarily disable StrictMode to prevent double renders that might interfere with WebChat
  // <StrictMode>
    <App />
  // </StrictMode>,
)
