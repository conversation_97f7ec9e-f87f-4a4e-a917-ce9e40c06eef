/**
 * Component-specific styling tokens
 */

import { brandColors, shadows } from './colors'
import { fontFamilies, fontSizes, fontWeights } from './typography'
import { spacing, borderRadius, borderWidths } from './spacing'

export const buttonStyles = {
  base: {
    fontFamily: fontFamilies.primary,
    fontSize: fontSizes.base,
    fontWeight: fontWeights.medium,
    padding: `${spacing[3]} ${spacing[5]}`,
    borderRadius: borderRadius.base,
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
    whiteSpace: 'nowrap' as const,
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing[2],
  },
  variants: {
    primary: {
      backgroundColor: brandColors.brandRed,
      color: brandColors.neutralWhite,
      '&:hover': {
        backgroundColor: brandColors.brandDarkRed,
        transform: 'translateY(-1px)',
        boxShadow: shadows.medium,
      },
      '&:active': {
        transform: 'translateY(0)',
        boxShadow: shadows.button,
      },
      '&:disabled': {
        backgroundColor: brandColors.neutral500,
        cursor: 'not-allowed',
        transform: 'none',
      },
    },
    secondary: {
      backgroundColor: brandColors.neutralWhite,
      color: brandColors.brandRed,
      border: `${borderWidths[2]} solid ${brandColors.brandRed}`,
      '&:hover': {
        backgroundColor: brandColors.brandRed,
        color: brandColors.neutralWhite,
        transform: 'translateY(-1px)',
        boxShadow: shadows.medium,
      },
    },
    success: {
      backgroundColor: brandColors.success,
      color: brandColors.neutralWhite,
      '&:hover': {
        backgroundColor: brandColors.successHover,
        transform: 'translateY(-1px)',
      },
    },
  },
  sizes: {
    sm: {
      padding: `${spacing[2]} ${spacing[4]}`,
      fontSize: fontSizes.sm,
    },
    md: {
      padding: `${spacing[3]} ${spacing[5]}`,
      fontSize: fontSizes.base,
    },
    lg: {
      padding: `${spacing[4]} ${spacing[6]}`,
      fontSize: fontSizes.lg,
    },
  },
} as const

export const cardStyles = {
  base: {
    backgroundColor: brandColors.neutralWhite,
    borderRadius: borderRadius.md,
    boxShadow: shadows.card,
    overflow: 'hidden' as const,
    border: `${borderWidths[1]} solid ${brandColors.neutral200}`,
  },
  variants: {
    elevated: {
      boxShadow: shadows.large,
      border: 'none',
    },
    outlined: {
      boxShadow: 'none',
      border: `${borderWidths[1]} solid ${brandColors.neutral300}`,
    },
  },
  padding: {
    none: spacing[0],
    sm: spacing[3],
    md: spacing[4],
    lg: spacing[6],
  },
} as const

export const inputStyles = {
  base: {
    fontFamily: fontFamilies.primary,
    fontSize: fontSizes.base,
    padding: `${spacing[3]} ${spacing[4]}`,
    border: `${borderWidths[2]} solid ${brandColors.neutral400}`,
    borderRadius: borderRadius.base,
    transition: 'border-color 0.2s, box-shadow 0.2s',
    width: '100%',
    '&:focus': {
      outline: 'none',
      borderColor: brandColors.brandBlue,
      boxShadow: `0 0 0 3px ${brandColors.focus}`,
    },
    '&:disabled': {
      backgroundColor: brandColors.disabled,
      cursor: 'not-allowed',
    },
  },
  variants: {
    error: {
      borderColor: brandColors.error,
      '&:focus': {
        borderColor: brandColors.error,
        boxShadow: `0 0 0 3px rgba(211, 47, 47, 0.1)`,
      },
    },
  },
} as const

export const statusIndicatorStyles = {
  base: {
    display: 'inline-flex',
    alignItems: 'center',
    gap: spacing[3],
    padding: spacing[3],
    borderRadius: borderRadius.base,
    fontSize: fontSizes.base,
    fontWeight: fontWeights.medium,
  },
  variants: {
    success: {
      backgroundColor: brandColors.neutral100,
      color: brandColors.success,
      border: `${borderWidths[1]} solid ${brandColors.success}`,
    },
    error: {
      backgroundColor: brandColors.errorBackground,
      color: brandColors.error,
      border: `${borderWidths[1]} solid ${brandColors.error}`,
    },
    warning: {
      backgroundColor: '#fff3cd',
      color: '#856404',
      border: `${borderWidths[1]} solid ${brandColors.warning}`,
    },
    info: {
      backgroundColor: '#d1ecf1',
      color: '#0c5460',
      border: `${borderWidths[1]} solid ${brandColors.info}`,
    },
  },
} as const

export const webChatStyles = {
  container: {
    width: '100%',
    maxWidth: '900px',
    height: '700px',
    backgroundColor: brandColors.neutralWhite,
    borderRadius: borderRadius.md,
    boxShadow: shadows.large,
    overflow: 'hidden' as const,
    display: 'flex',
    flexDirection: 'column' as const,
  },
  configPanel: {
    backgroundColor: brandColors.neutral100,
    padding: spacing[5],
    borderBottom: `${borderWidths[1]} solid ${brandColors.neutral300}`,
  },
  chatWrapper: {
    flex: 1,
    position: 'relative' as const,
    overflow: 'hidden' as const,
  },
  chat: {
    height: '100%',
    width: '100%',
    minHeight: '400px',
    position: 'relative' as const,
  },
} as const

export type ButtonStyles = typeof buttonStyles
export type CardStyles = typeof cardStyles
export type InputStyles = typeof inputStyles
export type StatusIndicatorStyles = typeof statusIndicatorStyles
export type WebChatStyles = typeof webChatStyles
