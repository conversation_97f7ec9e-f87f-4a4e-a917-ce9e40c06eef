/**
 * Spacing and layout tokens
 * Based on 4px grid system
 */

export const spacing = {
  0: '0',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  8: '32px',
  10: '40px',
  12: '48px',
  16: '64px',
  20: '80px',
  24: '96px',
  32: '128px',
} as const

export const borderRadius = {
  none: '0',
  sm: '4px',
  base: '8px',
  md: '12px',
  lg: '16px',
  xl: '20px',
  full: '9999px',
} as const

export const borderWidths = {
  0: '0',
  1: '1px',
  2: '2px',
  4: '4px',
} as const

export const breakpoints = {
  sm: '480px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
} as const

export const zIndex = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const

export const sizes = {
  xs: '320px',
  sm: '480px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  full: '100%',
  screen: '100vw',
  min: 'min-content',
  max: 'max-content',
} as const

export const maxWidths = {
  xs: '20rem',
  sm: '24rem',
  md: '28rem',
  lg: '32rem',
  xl: '36rem',
  '2xl': '42rem',
  '3xl': '48rem',
  '4xl': '56rem',
  '5xl': '64rem',
  '6xl': '72rem',
  '7xl': '80rem',
  full: '100%',
  screen: '100vw',
} as const

export type Spacing = typeof spacing
export type BorderRadius = typeof borderRadius
export type BorderWidths = typeof borderWidths
export type Breakpoints = typeof breakpoints
export type ZIndex = typeof zIndex
export type Sizes = typeof sizes
export type MaxWidths = typeof maxWidths
