/**
 * Centralized theme system
 * Exports all design tokens and theme utilities
 */

import { brandColors, gradients, shadows, opacity } from './colors'
import { fontFamilies, fontSizes, fontWeights, lineHeights, letterSpacing, textStyles } from './typography'
import { spacing, borderRadius, borderWidths, breakpoints, zIndex, sizes, maxWidths } from './spacing'
import { buttonStyles, cardStyles, inputStyles, statusIndicatorStyles, webChatStyles } from './components'

export const theme = {
  colors: brandColors,
  gradients,
  shadows,
  opacity,
  fonts: {
    families: fontFamilies,
    sizes: fontSizes,
    weights: fontWeights,
    lineHeights,
    letterSpacing,
    textStyles,
  },
  spacing,
  radii: borderRadius,
  borderWidths,
  breakpoints,
  zIndex,
  sizes,
  maxWidths,
  components: {
    button: buttonStyles,
    card: cardStyles,
    input: inputStyles,
    statusIndicator: statusIndicatorStyles,
    webChat: webChatStyles,
  },
} as const

// Helper functions for responsive design
export const mediaQueries = {
  sm: `@media (max-width: ${breakpoints.sm})`,
  md: `@media (max-width: ${breakpoints.md})`,
  lg: `@media (max-width: ${breakpoints.lg})`,
  xl: `@media (max-width: ${breakpoints.xl})`,
  minSm: `@media (min-width: ${breakpoints.sm})`,
  minMd: `@media (min-width: ${breakpoints.md})`,
  minLg: `@media (min-width: ${breakpoints.lg})`,
  minXl: `@media (min-width: ${breakpoints.xl})`,
} as const

// WebChat style set configuration for Bot Framework
export const createWebChatStyleSet = () => ({
  // Basic styling to ensure visibility
  rootHeight: '100%',
  rootWidth: '100%',
  
  // Brand colors
  primaryColor: brandColors.brandRed,
  accent: brandColors.brandOrange,
  
  // Typography
  fontFamily: fontFamilies.primary,
  
  // Message bubbles
  bubbleBackground: brandColors.neutralWhite,
  bubbleBorderColor: brandColors.neutral200,
  bubbleBorderRadius: parseInt(borderRadius.lg),
  bubbleFromUserBackground: gradients.primary,
  bubbleFromUserTextColor: brandColors.neutralWhite,
  
  // Send box
  sendBoxBackground: brandColors.neutralWhite,
  sendBoxBorderColor: brandColors.neutral200,
  sendBoxButtonColor: brandColors.brandRed,
  sendBoxButtonColorOnHover: brandColors.brandDarkRed,
  sendBoxTextColor: brandColors.neutral700,
  
  // Suggested actions
  suggestedActionBackground: brandColors.neutralWhite,
  suggestedActionBorderColor: brandColors.brandRed,
  suggestedActionTextColor: brandColors.brandRed,
  suggestedActionBackgroundOnHover: brandColors.brandRed,
  suggestedActionTextColorOnHover: brandColors.neutralWhite,
  
  // Timestamps and metadata
  timestampColor: brandColors.neutral600,
  
  // Typing indicator
  typingAnimationBackgroundImage: `url("data:image/svg+xml,%3csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' width='20px' height='20px' viewBox='0 0 50 50' style='enable-background:new 0 0 50 50;' xml:space='preserve'%3e%3cpath fill='%23${brandColors.neutral600.slice(1)}' d='M25.251,6.461c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615V6.461z'%3e%3canimateTransform attributeType='xml' attributeName='transform' type='rotate' from='0 25 25' to='360 25 25' dur='0.6s' repeatCount='indefinite'/%3e%3c/path%3e%3c/svg%3e")`,
})

// Export individual theme modules for direct access
export * from './colors'
export * from './typography'
export * from './spacing'
export * from './components'

export type Theme = typeof theme
export type MediaQueries = typeof mediaQueries

export default theme
