/**
 * Brand colors and design tokens
 * Based on the company's design system
 */

export const brandColors = {
  // Primary brand colors
  brandRed: '#E5384C',
  brandOrange: '#EA714F',
  brandDarkRed: '#D21242',
  brandLightRed: '#F9C7CC',

  // Secondary colors
  brandGreen: '#009b65',
  brandBlue: '#0078d4',
  brandDarkBlue: '#106ebe',

  // Neutrals
  neutralWhite: '#FFF',
  neutral50: '#FCFAFA',
  neutral100: '#F8F9FA',
  neutral200: '#F3F0F0',
  neutral300: '#E9ECEF',
  neutral400: '#DDD',
  neutral500: '#CCC',
  neutral600: '#716A6A',
  neutral700: '#2F2D2D',
  neutral800: '#212529',
  neutral900: '#000',

  // Semantic colors
  success: '#28a745',
  successHover: '#218838',
  warning: '#ffc107',
  error: '#d32f2f',
  errorBackground: '#ffebee',
  info: '#17a2b8',

  // Interactive states
  hover: 'rgba(0, 0, 0, 0.05)',
  active: 'rgba(0, 0, 0, 0.1)',
  focus: 'rgba(0, 120, 212, 0.1)',
  disabled: '#f5f5f5',
} as const

export const gradients = {
  primary: 'linear-gradient(90deg, #E5384C 0%, #EA714F 100%)',
  primaryHover: 'linear-gradient(90deg, #D21242 0%, #E5384C 100%)',
} as const

export const shadows = {
  small: '0 2px 4px rgba(229, 56, 76, 0.1)',
  medium: '0 4px 12px rgba(229, 56, 76, 0.3)',
  large: '0 10px 30px rgba(0, 0, 0, 0.3)',
  card: '0 2px 8px rgba(47, 45, 45, 0.1)',
  button: '0 2px 6px rgba(229, 56, 76, 0.2)',
} as const

export const opacity = {
  disabled: 0.6,
  hover: 0.8,
  overlay: 0.9,
} as const

export type BrandColors = typeof brandColors
export type Gradients = typeof gradients
export type Shadows = typeof shadows
export type Opacity = typeof opacity
