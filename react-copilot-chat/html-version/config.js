// Configuration for HTML version
//
// IMPORTANT: For the React version, use the .env file in react-copilot-chat/ folder instead
// This config.js is only for the standalone HTML version (index.html)
//
// Replace these values with your actual configuration:

window.BOT_CONFIG = {
  // Direct Line Configuration
  // Get your token from Azure Portal → Bot Service → Channels → Direct Line
  directLineToken: 'FeX3rEv7dZHJ2mzTD1nbDcQhNXnggSCdaG8t6T2cZA9r0phI51nFJQQJ99BFACi5YpzAArohAAABAZBSoW55.5kUiEQM7LVWLBNbIYaTo24b2Ablb0qwu0wwfMxCNZubwTNm0EDQ4JQQJ99BFACi5YpzAArohAAABAZBS38oT', // Replace with your actual token

  // Bot Configuration
  botName: 'Copilot Assistant',
  welcomeMessage: 'Hello! 👋 Welcome to our Copilot Studio integration. How can I help you today?',

  // Auto-connect settings
  autoConnect: false // Set to true to auto-connect when page loads (recommended for production)
};