<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Copilot Studio Web Chat POC</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }

        .chat-container {
            width: 100%;
            max-width: 800px;
            height: 600px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }

        .config-panel {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .config-panel input {
            flex: 1;
            min-width: 300px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .config-panel button {
            padding: 8px 16px;
            background: #0078d4;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .config-panel button:hover {
            background: #106ebe;
        }

        .config-panel button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        #webchat {
            height: calc(100% - 70px);
            width: 100%;
        }

        .status {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
        }

        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 6px;
            margin: 10px;
            border-left: 4px solid #d32f2f;
        }

        @media (max-width: 768px) {
            .chat-container {
                height: 70vh;
                margin: 0 10px;
            }

            .config-panel {
                flex-direction: column;
                align-items: stretch;
            }

            .config-panel input {
                min-width: auto;
            }
        }

        /* Enhanced Web Chat styling with your brand tokens */

        /* Suggested Actions (Buttons) */
        #webchat [role="button"][data-testid*="suggested-action"] {
            background-color: #FFF !important;
            border: 2px solid #E5384C !important;
            border-radius: 8px !important;
            color: #E5384C !important;
            font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            font-weight: 500 !important;
            padding: 8px 16px !important;
            margin: 4px !important;
            transition: all 0.2s ease-in-out !important;
            cursor: pointer !important;
            box-shadow: 0 2px 4px rgba(229, 56, 76, 0.1) !important;
        }

        #webchat [role="button"][data-testid*="suggested-action"]:hover {
            background-color: #E5384C !important;
            color: #FFF !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(229, 56, 76, 0.3) !important;
            border-color: #D21242 !important;
        }

        /* Adaptive Cards */
        #webchat .ac-adaptiveCard {
            background-color: #FFF !important;
            border: 1px solid #F3F0F0 !important;
            border-radius: 16px !important;
            box-shadow: 0 2px 8px rgba(47, 45, 45, 0.1) !important;
            padding: 16px !important;
            margin: 8px 0 !important;
            font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }

        /* Card Action Buttons */
        #webchat .ac-pushButton {
            background-color: #FFF !important;
            border: 2px solid #E5384C !important;
            border-radius: 8px !important;
            color: #E5384C !important;
            font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            font-weight: 500 !important;
            padding: 8px 16px !important;
            margin: 4px !important;
            transition: all 0.2s ease-in-out !important;
            cursor: pointer !important;
        }

        #webchat .ac-pushButton:hover {
            background-color: #E5384C !important;
            color: #FFF !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(229, 56, 76, 0.3) !important;
        }

        /* Quick Replies */
        #webchat [data-testid="quick-reply"] {
            background-color: #FFF !important;
            border: 2px solid #009b65 !important;
            border-radius: 8px !important;
            color: #009b65 !important;
            font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            font-weight: 500 !important;
            padding: 6px 12px !important;
            margin: 2px !important;
            transition: all 0.2s ease-in-out !important;
        }

        #webchat [data-testid="quick-reply"]:hover {
            background-color: #009b65 !important;
            color: #FFF !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 3px 8px rgba(0, 155, 101, 0.3) !important;
        }

        /* Send Box Button */
        #webchat [data-testid="send-button"] {
            background-color: #E5384C !important;
            border-radius: 8px !important;
            transition: all 0.2s ease-in-out !important;
        }

        #webchat [data-testid="send-button"]:hover {
            background-color: #D21242 !important;
            transform: scale(1.05) !important;
        }

        /* Message bubbles */
        #webchat [data-testid="message-bubble"] {
            font-family: 'Etelka', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            line-height: 1.5 !important;
        }

        /* Bot message bubbles */
        #webchat [data-testid="message-bubble"][data-from="bot"] {
            background-color: #FFF !important;
            border: 1px solid #F3F0F0 !important;
            color: #2F2D2D !important;
        }

        /* User message bubbles with gradient */
        #webchat [data-testid="message-bubble"][data-from="user"] {
            background: linear-gradient(90deg, #E5384C 0%, #EA714F 100%) !important;
            color: #FFF !important;
            border: none !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Copilot Studio Web Chat</h1>
        <p>Microsoft Bot Framework Integration POC</p>
    </div>

    <div class="chat-container">
        <div class="config-panel" id="configPanel">
            <!-- This will be populated by JavaScript based on configuration -->
        </div>

        <div id="webchat">
            <div class="status" id="initialStatus">
                <p>� Loading...</p>
                <small>Initializing chat interface</small>
            </div>
        </div>
    </div>

    <!-- Configuration -->
    <script src="config.js"></script>

    <!-- Microsoft Bot Framework Web Chat -->
    <script src="https://cdn.botframework.com/botframework-webchat/latest/webchat.js"></script>

    <script>
        let directLineToken = '';
        let chatInitialized = false;
        let useConfigToken = false;

        // Check if token is configured
        if (window.BOT_CONFIG && window.BOT_CONFIG.directLineToken &&
            window.BOT_CONFIG.directLineToken !== 'your_direct_line_token_here') {
            directLineToken = window.BOT_CONFIG.directLineToken;
            useConfigToken = true;
        }

        // Your company's design tokens
        const brandTokens = {
            brandRed: "#E5384C",
            brandOrange: "#EA714F",
            brandDarkRed: "#D21242",
            neutralWhite: "#FFF",
            neutral50: "#FCFAFA",
            neutral100: "#F8F6F6",
            neutral300: "#F3F0F0",
            neutral800: "#716A6A",
            neutral900: "#2F2D2D",
            accentGreen600: "#009b65",
            fontFamily: "Etelka, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
        };

        // Custom style set for Web Chat with your branding
        const styleSet = window.WebChat.createStyleSet({
            // Primary brand colors
            primaryColor: brandTokens.brandRed,
            accent: brandTokens.brandOrange,

            // Background colors
            backgroundColor: brandTokens.neutral50,

            // Chat bubble styling with your brand
            bubbleBackground: brandTokens.neutralWhite,
            bubbleFromUserBackground: `linear-gradient(90deg, ${brandTokens.brandRed} 0%, ${brandTokens.brandOrange} 100%)`,
            bubbleFromUserTextColor: brandTokens.neutralWhite,
            bubbleTextColor: brandTokens.neutral900,
            bubbleBorder: `1px solid ${brandTokens.neutral300}`,

            // Border radius
            bubbleBorderRadius: 8,
            bubbleFromUserBorderRadius: 8,

            // Avatar styling
            avatarSize: 40,
            botAvatarBackgroundColor: brandTokens.brandRed,
            userAvatarBackgroundColor: brandTokens.accentGreen600,

            // Input box styling
            sendBoxBackground: brandTokens.neutralWhite,
            sendBoxBorderTop: `2px solid ${brandTokens.neutral300}`,
            sendBoxTextColor: brandTokens.neutral900,
            sendBoxButtonColor: brandTokens.brandRed,
            sendBoxButtonColorOnHover: brandTokens.brandDarkRed,

            // Suggested actions with your brand styling
            suggestedActionBackground: brandTokens.neutralWhite,
            suggestedActionBorder: `2px solid ${brandTokens.brandRed}`,
            suggestedActionBorderOnHover: `2px solid ${brandTokens.brandDarkRed}`,
            suggestedActionTextColor: brandTokens.brandRed,
            suggestedActionTextColorOnHover: brandTokens.brandDarkRed,
            suggestedActionBorderRadius: 8,

            // Cards and rich content
            cardEmphasisBackgroundColor: brandTokens.neutral100,

            // Other elements
            scrollToEndButtonBackgroundColor: brandTokens.brandRed,
            timestampColor: brandTokens.neutral800,
            primaryFont: brandTokens.fontFamily
        });

        // Customize bubble styles
        styleSet.textContent = {
            ...styleSet.textContent,
            fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
            fontSize: '14px',
            lineHeight: 1.4
        };

        function initializeChat() {
            const tokenInput = document.getElementById('tokenInput');
            const connectBtn = document.getElementById('connectBtn');
            const webchatDiv = document.getElementById('webchat');

            if (!useConfigToken) {
                directLineToken = tokenInput.value.trim();

                if (!directLineToken) {
                    showError('Please enter a Direct Line token');
                    return;
                }
            }

            connectBtn.disabled = true;
            connectBtn.textContent = 'Connecting...';

            try {
                // Clear any existing content
                webchatDiv.innerHTML = '<div class="status"><p>🔄 Connecting to your Copilot...</p></div>';

                // Create Direct Line connection
                const directLine = window.WebChat.createDirectLine({
                    token: directLineToken
                });

                // Custom activity middleware for welcome message
                const activityMiddleware = () => next => card => {
                    return next(card);
                };

                // Render Web Chat
                window.WebChat.renderWebChat(
                    {
                        directLine: directLine,
                        styleSet: styleSet,
                        activityMiddleware: activityMiddleware,

                        // User and bot avatars
                        userAvatarInitials: 'You',
                        botAvatarInitials: '🤖',

                        // Locale and other options
                        locale: 'en-US',

                        // Send welcome message after connection
                        onLoad: () => {
                            // Send a welcome activity
                            setTimeout(() => {
                                directLine.postActivity({
                                    type: 'event',
                                    name: 'webchat/join',
                                    from: { id: 'user' }
                                }).subscribe();
                            }, 1000);
                        }
                    },
                    webchatDiv
                );

                // Handle connection status
                directLine.connectionStatus$.subscribe(connectionStatus => {
                    console.log('Connection status:', connectionStatus);

                    if (connectionStatus === 2) { // Connected
                        connectBtn.textContent = '✅ Connected';
                        connectBtn.style.background = '#28a745';
                        chatInitialized = true;

                        // Send welcome message using configuration
                        setTimeout(() => {
                            const welcomeMessage = window.BOT_CONFIG?.welcomeMessage || 'Hello! 👋 Welcome to our Copilot Studio integration. How can I help you today?';
                            const botName = window.BOT_CONFIG?.botName || 'Copilot Assistant';

                            directLine.postActivity({
                                type: 'message',
                                text: welcomeMessage,
                                from: { id: 'bot', name: botName }
                            }).subscribe();
                        }, 1500);

                    } else if (connectionStatus === 4) { // Failed
                        showError('Failed to connect. Please check your token and try again.');
                        resetConnection();
                    }
                });

            } catch (error) {
                console.error('Error initializing chat:', error);
                showError('Error initializing chat: ' + error.message);
                resetConnection();
            }
        }

        function showError(message) {
            const webchatDiv = document.getElementById('webchat');
            webchatDiv.innerHTML = `
                <div class="status">
                    <div class="error">
                        <strong>❌ Error:</strong> ${message}
                    </div>
                    <p>Please check your Direct Line token and try again.</p>
                </div>
            `;
        }

        function resetConnection() {
            const connectBtn = document.getElementById('connectBtn');
            connectBtn.disabled = false;
            connectBtn.textContent = 'Connect Chat';
            connectBtn.style.background = '#0078d4';
            chatInitialized = false;
        }



        // Initialize UI based on configuration
        function initializeUI() {
            const configPanel = document.getElementById('configPanel');
            const initialStatus = document.getElementById('initialStatus');

            if (useConfigToken) {
                // Show configured token status
                configPanel.innerHTML = `
                    <div style="display: flex; gap: 15px; align-items: center; justify-content: space-between; flex-wrap: wrap;">
                        <div style="display: flex; align-items: center; gap: 12px; flex: 1;">
                            <span style="font-size: 24px; color: #28a745;">🔐</span>
                            <div>
                                <p style="margin: 0; font-weight: 600; color: #2F2D2D; font-size: 14px;"><strong>Using Configured Token</strong></p>
                                <small style="color: #716A6A; font-size: 12px;">Token loaded from config.js</small>
                            </div>
                        </div>
                        <div>
                            <button id="connectBtn" onclick="initializeChat()" style="padding: 8px 16px; background: #0078d4; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">Connect Chat</button>
                        </div>
                    </div>
                `;

                // Update status message
                initialStatus.innerHTML = `
                    <p>🔐 Token configured - Ready to connect</p>
                    <small>Click "Connect Chat" above to start</small>
                `;

                // Auto-connect if enabled
                if (window.BOT_CONFIG?.autoConnect) {
                    initialStatus.innerHTML = `
                        <p>🔄 Auto-connecting...</p>
                        <small>Using configured token</small>
                    `;
                    setTimeout(() => {
                        initializeChat();
                    }, 1000);
                }
            } else {
                // Show manual token input
                configPanel.innerHTML = `
                    <input
                        type="password"
                        id="tokenInput"
                        placeholder="Enter your Direct Line token here..."
                        value=""
                        style="flex: 1; min-width: 300px; padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;"
                    >
                    <button id="connectBtn" onclick="initializeChat()" style="padding: 8px 16px; background: #0078d4; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">Connect Chat</button>
                `;

                // Add enter key handler for manual input
                setTimeout(() => {
                    const tokenInput = document.getElementById('tokenInput');
                    if (tokenInput) {
                        tokenInput.addEventListener('keypress', function(e) {
                            if (e.key === 'Enter') {
                                initializeChat();
                            }
                        });
                        tokenInput.focus();
                    }
                }, 100);

                // Update status message for manual input
                initialStatus.innerHTML = `
                    <p>👆 Enter your Direct Line token above to start chatting</p>
                    <small>Get your token from Azure Bot Service → Channels → Direct Line</small>
                `;
            }
        }

        // Initialize UI when page loads
        window.addEventListener('load', function() {
            initializeUI();
        });
    </script>
</body>
</html>