# Custom Interactive Elements with Your Brand Styling

This guide shows how to implement custom interactive elements in your Copilot Studio bot using your company's design tokens.

## 🎨 Brand Tokens Applied

Your design system has been integrated with the following mappings:

### Colors
- **Primary Brand**: `#E5384C` (brandRed) - Used for primary buttons and user messages
- **Secondary Brand**: `#EA714F` (brandOrange) - Used in gradients and accents
- **Success/Green**: `#009b65` (accentGreen600) - Used for quick replies and success states
- **Neutrals**: Various grays from your palette for backgrounds and text

### Typography
- **Font Family**: `Etelka` - Your brand font applied to all text elements
- **Font Weights**: 300 (light), 500 (medium), 700 (bold) as per your tokens

### Spacing & Layout
- **Grid System**: 4px base unit applied to padding and margins
- **Border Radius**: 8px (small), 16px (medium) for cards and buttons

## 🔧 Interactive Elements You Can Style

### 1. Suggested Actions (Buttons)

**What they are**: Action buttons that appear below bot messages
**Your styling**: Red border, white background, hover effects with brand colors

```json
{
  "type": "message",
  "text": "What would you like to do?",
  "suggestedActions": {
    "actions": [
      {
        "type": "imBack",
        "title": "View Account",
        "value": "show account"
      },
      {
        "type": "imBack",
        "title": "Pay Bill",
        "value": "pay bill"
      },
      {
        "type": "openUrl",
        "title": "Contact Support",
        "value": "https://support.example.com"
      }
    ]
  }
}
```

### 2. Adaptive Cards

**What they are**: Rich, interactive cards with custom layouts
**Your styling**: White background, rounded corners, brand button styling

```json
{
  "type": "message",
  "attachments": [
    {
      "contentType": "application/vnd.microsoft.card.adaptive",
      "content": {
        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
        "type": "AdaptiveCard",
        "version": "1.3",
        "body": [
          {
            "type": "TextBlock",
            "text": "Account Summary",
            "weight": "Bolder",
            "size": "Medium",
            "color": "Default"
          },
          {
            "type": "FactSet",
            "facts": [
              {
                "title": "Current Balance:",
                "value": "$1,234.56"
              },
              {
                "title": "Due Date:",
                "value": "March 15, 2024"
              }
            ]
          }
        ],
        "actions": [
          {
            "type": "Action.Submit",
            "title": "Pay Now",
            "data": {
              "action": "pay_bill"
            }
          },
          {
            "type": "Action.OpenUrl",
            "title": "View Details",
            "url": "https://account.example.com"
          }
        ]
      }
    }
  ]
}
```

### 3. Hero Cards

**What they are**: Cards with images, titles, and action buttons
**Your styling**: Rounded corners, brand button styling

```json
{
  "type": "message",
  "attachments": [
    {
      "contentType": "application/vnd.microsoft.card.hero",
      "content": {
        "title": "Energy Saving Tips",
        "subtitle": "Reduce your monthly bill",
        "text": "Learn how to save energy and money with these simple tips.",
        "images": [
          {
            "url": "https://example.com/energy-tips.jpg"
          }
        ],
        "buttons": [
          {
            "type": "imBack",
            "title": "Get Tips",
            "value": "show energy tips"
          },
          {
            "type": "openUrl",
            "title": "Learn More",
            "value": "https://example.com/energy-tips"
          }
        ]
      }
    }
  ]
}
```

### 4. Quick Replies

**What they are**: Small, pill-shaped buttons for quick responses
**Your styling**: Green accent color (accentGreen600) with hover effects

```json
{
  "type": "message",
  "text": "How can I help you today?",
  "channelData": {
    "quickReplies": [
      {
        "contentType": "text/plain",
        "title": "Account Info",
        "payload": "account"
      },
      {
        "contentType": "text/plain",
        "title": "Billing",
        "payload": "billing"
      },
      {
        "contentType": "text/plain",
        "title": "Support",
        "payload": "support"
      }
    ]
  }
}
```

### 5. Carousel Cards

**What they are**: Horizontally scrollable cards
**Your styling**: Consistent with your card styling, rounded corners

```json
{
  "type": "message",
  "attachments": [
    {
      "contentType": "application/vnd.microsoft.card.hero",
      "content": {
        "title": "Electricity Plan",
        "subtitle": "$0.12/kWh",
        "text": "Fixed rate for 12 months",
        "buttons": [
          {
            "type": "imBack",
            "title": "Select Plan",
            "value": "select electricity plan"
          }
        ]
      }
    },
    {
      "contentType": "application/vnd.microsoft.card.hero",
      "content": {
        "title": "Gas Plan",
        "subtitle": "$0.08/therm",
        "text": "Variable rate with green options",
        "buttons": [
          {
            "type": "imBack",
            "title": "Select Plan",
            "value": "select gas plan"
          }
        ]
      }
    }
  ],
  "attachmentLayout": "carousel"
}
```

## 🎯 How to Test Your Styling

1. **Connect your bot** using the Direct Line token
2. **Send test messages** from your Copilot Studio that include these interactive elements
3. **Verify the styling** matches your brand guidelines
4. **Test hover effects** and interactions

## 🔧 Customization Options

### Button Variants
You can create different button styles by modifying the CSS:

```css
/* Primary buttons (red) */
.webchat [data-button-type="primary"] {
  background-color: #E5384C !important;
  color: #FFF !important;
  border: 2px solid #E5384C !important;
}

/* Secondary buttons (green) */
.webchat [data-button-type="secondary"] {
  background-color: #FFF !important;
  color: #009b65 !important;
  border: 2px solid #009b65 !important;
}
```

### Card Themes
Apply different themes to cards:

```css
/* Success theme */
.webchat .card-success {
  border-left: 4px solid #009b65 !important;
  background-color: #F2F7EC !important;
}

/* Warning theme */
.webchat .card-warning {
  border-left: 4px solid #FCCA6D !important;
  background-color: #FFFAF0 !important;
}
```

## 📱 Responsive Design

All styling includes responsive breakpoints that match your design system:

- **Mobile**: Optimized for touch interactions
- **Tablet**: Balanced layout for medium screens
- **Desktop**: Full feature set with hover effects

## 🚀 Next Steps

1. **Test with your Copilot Studio bot** using real content
2. **Customize colors** by modifying the brand tokens in the code
3. **Add more interactive elements** as needed for your use case
4. **Implement accessibility features** following your design system guidelines

Your Web Chat integration now fully reflects your brand identity while maintaining excellent user experience!