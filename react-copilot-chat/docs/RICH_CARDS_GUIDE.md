# Rich Cards Implementation Guide

## Overview

This guide explains how to implement and use rich cards in the Microsoft Bot Framework WebChat interface. Our enhanced implementation supports Adaptive Cards, Hero Cards, Carousels, Data Visualizations, and Media Attachments with full responsive design and accessibility features.

## Supported Rich Card Types

### 1. Hero Cards
Hero cards display a title, subtitle, text, image, and action buttons.

```javascript
// Bot Framework - Sending a Hero Card
const heroCard = {
  contentType: 'application/vnd.microsoft.card.hero',
  content: {
    title: 'Welcome to Our Service',
    subtitle: 'Premium solutions for your business',
    text: 'Discover our comprehensive suite of tools designed to help your business grow and succeed in today\'s competitive market.',
    images: [
      {
        url: 'https://example.com/hero-image.jpg',
        alt: 'Service overview'
      }
    ],
    buttons: [
      {
        type: 'postBack',
        title: 'Get Started',
        value: 'get_started'
      },
      {
        type: 'openUrl',
        title: 'Learn More',
        value: 'https://example.com/learn-more'
      }
    ]
  }
}

await context.sendActivity({
  attachments: [heroCard]
})
```

### 2. Adaptive Cards
Adaptive Cards provide rich, interactive layouts with forms, inputs, and actions.

```javascript
// Bot Framework - Sending an Adaptive Card
const adaptiveCard = {
  contentType: 'application/vnd.microsoft.card.adaptive',
  content: {
    type: 'AdaptiveCard',
    version: '1.3',
    body: [
      {
        type: 'TextBlock',
        text: 'Customer Feedback',
        weight: 'Bolder',
        size: 'Medium',
        color: 'Accent'
      },
      {
        type: 'TextBlock',
        text: 'Please rate your experience:',
        wrap: true
      },
      {
        type: 'Input.ChoiceSet',
        id: 'rating',
        style: 'compact',
        choices: [
          { title: '⭐ Excellent', value: '5' },
          { title: '⭐ Good', value: '4' },
          { title: '⭐ Average', value: '3' },
          { title: '⭐ Poor', value: '2' },
          { title: '⭐ Very Poor', value: '1' }
        ]
      }
    ],
    actions: [
      {
        type: 'Action.Submit',
        title: 'Submit Feedback',
        data: { action: 'submit_feedback' }
      }
    ]
  }
}

await context.sendActivity({
  attachments: [adaptiveCard]
})
```

### 3. Carousel Cards
Display multiple cards in a horizontal scrollable layout.

```javascript
// Bot Framework - Sending a Carousel
const carouselAttachment = {
  contentType: 'application/vnd.microsoft.card.carousel',
  content: {
    attachmentLayout: 'carousel',
    attachments: [
      {
        contentType: 'application/vnd.microsoft.card.hero',
        content: {
          title: 'Product A',
          subtitle: '$99.99',
          text: 'High-quality product with excellent features',
          images: [{ url: 'https://example.com/product-a.jpg' }],
          buttons: [
            { type: 'postBack', title: 'Buy Now', value: 'buy_product_a' }
          ]
        }
      },
      {
        contentType: 'application/vnd.microsoft.card.hero',
        content: {
          title: 'Product B',
          subtitle: '$149.99',
          text: 'Premium product with advanced capabilities',
          images: [{ url: 'https://example.com/product-b.jpg' }],
          buttons: [
            { type: 'postBack', title: 'Buy Now', value: 'buy_product_b' }
          ]
        }
      }
    ]
  }
}

await context.sendActivity({
  attachments: [carouselAttachment]
})
```

### 4. Data Visualization Cards
Send structured data for charts and graphs.

```javascript
// Custom Data Visualization Card
const chartCard = {
  contentType: 'application/vnd.custom.chart',
  content: {
    type: 'bar',
    title: 'Sales Performance',
    subtitle: 'Q4 2024 Results',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr'],
      datasets: [
        {
          label: 'Sales',
          data: [12000, 19000, 15000, 25000]
        }
      ]
    }
  }
}

await context.sendActivity({
  attachments: [chartCard]
})
```

### 5. Media Attachments
Send images, videos, audio files, and documents.

```javascript
// Image Attachment
const imageAttachment = {
  contentType: 'image/jpeg',
  contentUrl: 'https://example.com/image.jpg',
  name: 'product-image.jpg'
}

// Video Attachment
const videoAttachment = {
  contentType: 'video/mp4',
  contentUrl: 'https://example.com/video.mp4',
  name: 'product-demo.mp4'
}

// File Attachment
const fileAttachment = {
  contentType: 'application/pdf',
  contentUrl: 'https://example.com/document.pdf',
  name: 'product-catalog.pdf'
}

await context.sendActivity({
  attachments: [imageAttachment, videoAttachment, fileAttachment]
})
```

## Button Types and Actions

### Supported Button Types

1. **postBack** - Sends a message back to the bot
2. **openUrl** - Opens a URL in a new tab
3. **call** - Initiates a phone call
4. **playAudio** - Plays an audio file
5. **playVideo** - Plays a video file
6. **showImage** - Displays an image
7. **downloadFile** - Downloads a file

```javascript
const buttons = [
  {
    type: 'postBack',
    title: 'Send Message',
    value: 'user_clicked_button'
  },
  {
    type: 'openUrl',
    title: 'Visit Website',
    value: 'https://example.com'
  },
  {
    type: 'call',
    title: 'Call Support',
    value: 'tel:+1234567890'
  }
]
```

## Styling and Theming

### Brand Colors
The rich cards automatically use your brand colors:
- Primary: #E5384C (Brand Red)
- Secondary: #EA714F (Brand Orange)
- Success: #009b65 (Brand Green)
- Neutral: Various shades for text and backgrounds

### Responsive Design
All rich cards are automatically responsive:
- **Desktop**: Full-width cards with side-by-side layouts
- **Tablet**: Adjusted spacing and font sizes
- **Mobile**: Stacked layouts with touch-friendly buttons

### Accessibility Features
- ARIA labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Focus management
- Reduced motion support

## Testing Rich Cards

### Using the Rich Cards Demo
1. Navigate to your WebChat application
2. Click the mode switcher button until you reach "🎨 Rich Cards"
3. View examples of all supported card types
4. Test responsive behavior by resizing the browser

### Testing in Your Bot
1. Implement the card examples in your bot code
2. Test in the WebChat interface
3. Verify responsive behavior on different devices
4. Check accessibility with screen readers

## Best Practices

### Card Design
- Keep titles concise (max 50 characters)
- Use high-quality images (recommended: 300x200px)
- Limit buttons to 3-5 per card
- Provide meaningful alt text for images

### Performance
- Optimize images for web (use WebP when possible)
- Keep carousel cards to 5-7 items maximum
- Use lazy loading for media content
- Compress large files before sending

### Accessibility
- Always include alt text for images
- Use descriptive button labels
- Ensure sufficient color contrast
- Test with keyboard navigation

### User Experience
- Use cards to reduce cognitive load
- Group related actions together
- Provide clear call-to-action buttons
- Show loading states for slow content

## Troubleshooting

### Common Issues

1. **Cards not rendering**: Check content type and structure
2. **Images not loading**: Verify URLs are accessible and HTTPS
3. **Buttons not working**: Ensure proper button type and value
4. **Styling issues**: Check theme configuration and CSS conflicts

### Debug Mode
Use the debug mode in the application to:
- Test token connectivity
- View detailed error messages
- Validate card structures
- Check network requests

## Advanced Features

### Custom Card Types
You can extend the system with custom card types by:
1. Creating custom renderers
2. Adding new content types
3. Implementing custom styling
4. Registering with the activity middleware

### Integration with External APIs
Rich cards can integrate with:
- Chart.js for data visualization
- Media players for audio/video
- File viewers for documents
- Custom business logic

## Support and Resources

- **Documentation**: See the `/docs` folder for detailed guides
- **Examples**: Check the `SampleRichContent.tsx` component
- **Troubleshooting**: Use the built-in debug mode
- **Customization**: Modify the theme system for brand alignment

For additional support, refer to the Microsoft Bot Framework documentation and the Adaptive Cards schema reference.
