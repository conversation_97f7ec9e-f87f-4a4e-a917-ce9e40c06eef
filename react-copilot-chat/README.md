# 🤖 Copilot Studio Web Chat Integration POC

A proof-of-concept implementation for integrating Microsoft Copilot Studio agents into custom web applications using the Bot Framework Web Chat component.

## 📋 Overview

This project provides two implementations:
1. **HTML/Vanilla JS** - Simple, standalone HTML page
2. **React + Vite** - Modern React application with component-based architecture

Both implementations include:
- ✅ Direct Line channel integration
- ✅ **Full brand integration** with your design tokens
- ✅ **Custom interactive elements** (buttons, cards, quick replies)
- ✅ **Brand typography** using Etelka font family
- ✅ **Brand colors** (#E5384C red, #EA714F orange, #009b65 green)
- ✅ Welcome message functionality
- ✅ Responsive design with 4px grid system
- ✅ Error handling and connection status
- ✅ **Enhanced hover effects** and animations

## 🚀 Quick Start

### Prerequisites

1. **Copilot Studio Agent**: Published and connected to Direct Line channel in Azure
2. **Direct Line Token**: Generated from Azure Bot Service
3. **Node.js** (for React version): Version 16+ recommended

### Primary: React Version (Recommended)

**Quick Setup:**
```bash
# 1. Copy environment template and configure
cp .env.example .env
# Edit .env and replace your_direct_line_token_here with your actual token

# 2. Install dependencies and start
npm install
npm run dev

# 3. Open http://localhost:5173 in your browser
# 🎉 Auto-connects automatically - no manual token entry needed!
```

**Alternative (Manual Token):**
- If no `.env` file is configured, you can still enter the token manually in the UI

### Alternative: HTML Version (Standalone)

**Quick Start (Manual Token):**
1. Open `html-version/index.html` in any modern web browser
2. Enter your Direct Line token in the input field
3. Click "Connect Chat" to start chatting

**Recommended Setup (Environment Configuration):**
1. Edit `html-version/config.js` and replace `your_direct_line_token_here` with your actual token
2. Set `autoConnect: true` for automatic connection
3. Open `html-version/index.html` in any modern web browser

```bash
# Simply open the file
open html-version/index.html
# or serve locally
python -m http.server 8000
```

## 🔧 Configuration

### Getting Your Direct Line Token

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to your Bot Service resource
3. Go to **Channels** → **Direct Line**
4. Generate a new token or copy an existing one
5. Configure it using one of the methods below

### Environment Configuration (Recommended)

**For React Version (Primary Configuration):**
```bash
# Navigate to React directory and copy the template
cd react-copilot-chat
cp .env.example .env

# Edit the .env file
VITE_DIRECT_LINE_TOKEN=your_actual_token_here
VITE_BOT_NAME=Your Bot Name
VITE_WELCOME_MESSAGE=Your custom welcome message
```

**For HTML Version (Standalone):**
```javascript
// Edit config.js in the root directory
window.BOT_CONFIG = {
  directLineToken: 'your_actual_token_here',
  botName: 'Your Bot Name',
  welcomeMessage: 'Your custom welcome message',
  autoConnect: true // Set to true for automatic connection
};
```

> **Note**: The React version uses `.env` files in the `react-copilot-chat/` folder, while the HTML version uses `config.js` in the root. This keeps configuration separate and clear for each implementation.

### Security Benefits
- ✅ **No manual token entry** required
- ✅ **Tokens stored securely** in environment files
- ✅ **Auto-connection** for seamless user experience
- ✅ **Easy deployment** with different tokens per environment

### Customization Options

#### Brand Integration
Both implementations now use your complete design system:

```javascript
// Your brand tokens integrated
const brandTokens = {
  brandRed: "#E5384C",           // Primary brand color
  brandOrange: "#EA714F",        // Secondary brand color
  brandDarkRed: "#D21242",       // Hover states
  accentGreen600: "#009b65",     // Success/accent color
  fontFamily: "Etelka",          // Brand typography
  // ... complete design system
}

// Applied to Web Chat styling
bubbleFromUserBackground: 'linear-gradient(90deg, #E5384C 0%, #EA714F 100%)',
suggestedActionBorder: '2px solid #E5384C',
primaryFont: 'Etelka, Segoe UI, ...',
```

#### Avatars
- **Bot Avatar**: Currently set to 🤖 emoji
- **User Avatar**: Shows "You" initials
- Can be customized to use image URLs or different initials

#### Interactive Elements
Your brand styling is applied to all interactive components:

- **Suggested Actions**: Red border buttons with hover effects
- **Adaptive Cards**: White background with brand button styling
- **Quick Replies**: Green accent color for quick responses
- **Hero Cards**: Rounded corners with brand typography
- **Carousel Cards**: Consistent brand styling across all cards

#### Welcome Message
The welcome message is automatically sent after connection:
```javascript
'Hello! 👋 Welcome to our Copilot Studio integration. How can I help you today?'
```

## 📁 Project Structure

```
copilot-studio-webchat/
├── .env.example                   # Environment template (copy to .env)
├── .env                          # Your environment config (create from template)
├── src/                          # React application source
│   ├── components/
│   │   ├── WebChatComponent.jsx  # Main Web Chat component
│   │   └── WebChatComponent.css  # Brand styling
│   ├── App.jsx                   # Main App component
│   ├── App.css                   # App styling
│   └── index.css                 # Global styles
├── html-version/                 # Alternative HTML implementation
│   ├── index.html               # Standalone HTML version
│   └── config.js                # Configuration for HTML version
├── public/                       # Static assets
├── package.json                  # Project dependencies and scripts
├── vite.config.js               # Vite configuration
├── README.md                     # This file
├── SETUP.md                      # Quick setup guide
└── custom-elements-guide.md      # Interactive elements guide
```

### Configuration
- **Primary**: Use `.env` file for React version (recommended)
- **Alternative**: Use `html-version/config.js` for standalone HTML
- **Security**: Environment files keep tokens out of source code

## 🎨 UI Features

### Custom Styling
- **Modern gradient background**
- **Rounded chat bubbles** with custom colors
- **Responsive design** for mobile and desktop
- **Smooth animations** and transitions
- **Professional typography** using Segoe UI font family

### Interactive Elements
- **Token input** with password masking
- **Connection status** indicators
- **Error handling** with user-friendly messages
- **Loading states** during connection

### Responsive Design
- **Mobile-first** approach
- **Flexible layouts** that adapt to screen size
- **Touch-friendly** buttons and inputs

## 🔒 Security Considerations

⚠️ **Important**: This is a POC implementation. For production use:

1. **Never expose Direct Line secrets** in client-side code
2. **Implement token refresh** mechanism
3. **Use server-side token generation** with proper authentication
4. **Add rate limiting** and abuse protection
5. **Implement proper error logging** and monitoring

## 🛠️ Development

### HTML Version
- No build process required
- Can be served from any web server
- Easy to customize and deploy

### React Version
- Built with Vite for fast development
- Hot module replacement for instant updates
- Modern ES6+ JavaScript
- Component-based architecture

### Available Scripts (React)
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

## 📦 Dependencies

### HTML Version
- Microsoft Bot Framework Web Chat (CDN)
- No additional dependencies

### React Version
- React 18+
- Vite
- botframework-webchat
- Modern browser with ES6+ support

## 🐛 Troubleshooting

### Common Issues

1. **"Failed to connect" error**
   - Verify your Direct Line token is correct
   - Check that your bot is published and Direct Line channel is enabled
   - Ensure token hasn't expired

2. **Chat not loading**
   - Check browser console for JavaScript errors
   - Verify internet connection
   - Try refreshing the page

3. **Styling issues**
   - Clear browser cache
   - Check for CSS conflicts
   - Verify all CSS files are loading

### Debug Mode
Enable browser developer tools to see:
- Connection status logs
- Network requests
- JavaScript errors
- WebSocket communication

## 🚀 Next Steps

For production deployment, consider:

1. **Backend Integration**
   - Implement server-side token management
   - Add user authentication
   - Create API endpoints for bot communication

2. **Enhanced Features**
   - File upload support
   - Rich card rendering
   - Speech-to-text integration
   - Multi-language support

3. **Monitoring & Analytics**
   - Conversation logging
   - User engagement metrics
   - Error tracking and alerting

## 📄 License

This project is provided as-is for educational and demonstration purposes.

## 🤝 Contributing

This is a POC project. For production use cases, please implement proper security measures and follow Microsoft's best practices for Bot Framework applications.

---

**Built with ❤️ using Microsoft Bot Framework Web Chat**