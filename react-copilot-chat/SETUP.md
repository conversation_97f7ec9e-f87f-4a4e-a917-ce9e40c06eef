# 🚀 Quick Setup Guide

## Step 1: Get Your Direct Line Token

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to your Bot Service resource
3. Go to **Channels** → **Direct Line**
4. Generate a new token or copy an existing one
5. Copy the token (you'll need it in the next step)

## Step 2: Choose Your Implementation

### Option A: React Version (Recommended)

```bash
# 1. Copy environment template
cp .env.example .env

# 2. Edit .env file and replace your_direct_line_token_here with your actual token
# VITE_DIRECT_LINE_TOKEN=your_actual_token_here

# 3. Install and run
npm install
npm run dev

# 4. Open http://localhost:5173 - it will auto-connect! 🎉
```

### Option B: HTML Version (Standalone)

```bash
# 1. Edit html-version/config.js
# Replace 'your_direct_line_token_here' with your actual token
# Set autoConnect: true for automatic connection

# 2. Open html-version/index.html in your browser
open html-version/index.html
```

## Step 3: Test Your Integration

1. **Verify Connection**: The chat should connect automatically
2. **Test Interactions**: Try sending messages to your Copilot
3. **Check Styling**: Verify your brand colors and styling are applied
4. **Test Interactive Elements**: Try buttons, cards, and quick replies

## 🔒 Security Notes

- ✅ **Environment files** (`.env`) are automatically ignored by git
- ✅ **No hardcoded tokens** in your source code
- ✅ **Easy deployment** with different tokens per environment
- ⚠️ **Don't commit** real tokens to version control

## 🎨 Customization

Your brand styling is already applied! The integration uses:
- **Brand Colors**: Red (#E5384C), Orange (#EA714F), Green (#009b65)
- **Typography**: Etelka font family
- **Interactive Elements**: Buttons, cards, and quick replies with your brand styling

## 🆘 Need Help?

- Check the main [README.md](README.md) for detailed documentation
- Review [custom-elements-guide.md](custom-elements-guide.md) for interactive elements
- Ensure your Copilot Studio bot is published and connected to Direct Line channel

---

**That's it! Your branded Copilot Studio Web Chat integration is ready to use! 🎉**