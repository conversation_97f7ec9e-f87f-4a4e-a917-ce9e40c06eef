{"name": "copilot-studio-webchat", "description": "Branded Web Chat integration for Microsoft Copilot Studio with custom styling and interactive elements", "version": "1.0.0", "type": "module", "private": true, "keywords": ["copilot-studio", "webchat", "bot-framework", "react", "chatbot"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "start": "npm run dev", "serve": "npm run preview"}, "dependencies": {"botframework-webchat": "^4.18.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}