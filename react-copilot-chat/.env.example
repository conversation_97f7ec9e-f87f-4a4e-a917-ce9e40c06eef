# Environment Variables Configuration
# Copy this file to .env and replace with your actual values

# Direct Line Configuration
# Get your token from Azure Portal → Bot Service → Channels → Direct Line
VITE_DIRECT_LINE_TOKEN=your_direct_line_token_here

# Optional: Direct Line Secret (for server-side token generation)
# VITE_DIRECT_LINE_SECRET=your_direct_line_secret_here

# Bot Configuration
VITE_BOT_NAME=Copilot Assistant
VITE_WELCOME_MESSAGE=Hello! 👋 Welcome to our Copilot Studio integration. How can I help you today?

# Environment
VITE_ENVIRONMENT=development